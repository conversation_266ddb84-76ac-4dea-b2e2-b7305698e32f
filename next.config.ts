import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  productionBrowserSourceMaps: false,
  async redirects() {
    return [
      // Basic redirect
      {
        source: "/about/:slug",
        destination: "/about",
        permanent: true,
      },
      // Wildcard path matching
      {
        source: "/project-detail/:slug",
        destination: "/projects/:slug",
        permanent: true,
      },
    ];
  },
  cleanDistDir: true,
  compress: true,
  reactStrictMode: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },

  experimental: {
    webpackMemoryOptimizations: false,
    serverSourceMaps: false,

    staleTimes: {
      dynamic: 30,
    },
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    unoptimized: true,
    formats: ["image/avif", "image/webp"],
    deviceSizes: [300, 480, 768, 1024, 1200, 1600],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "console.42estates.com",
        pathname: "/uploads/**",
      },
    ],
  },
};

export default nextConfig;
