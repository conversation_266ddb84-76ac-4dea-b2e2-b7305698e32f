{"name": "42estates", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap", "knip": "knip"}, "dependencies": {"@hookform/resolvers": "^4.0.0", "@next/third-parties": "^15.2.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "framer-motion": "^11.16.0", "html-react-parser": "^5.2.2", "lucide-react": "^0.456.0", "next": "15.0.3", "next-sitemap": "^4.2.3", "qs": "^6.13.0", "react": "18.2.0", "react-countup": "^6.5.3", "react-dom": "18.2.0", "react-hook-form": "^7.54.2", "react-zoom-pan-pinch": "^3.6.1", "swiper": "^11.2.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.17.6", "@types/qs": "^6.9.17", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "knip": "^5.36.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.6.3"}}