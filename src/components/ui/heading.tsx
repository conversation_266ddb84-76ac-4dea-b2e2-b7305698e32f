import React from "react";
import { twMerge } from "tailwind-merge";

type HeadingLevel = "hero" | "hero2" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6";

interface HeadingProps extends React.HTMLAttributes<HTMLHeadingElement> {
  level?: HeadingLevel;
  children: React.ReactNode;
}

const headingStyles: Record<HeadingLevel, string> = {
  hero: "text-[42px] leading-[50px] md:text-[52px] lg:text-[52px] lg:leading-[56px] font-medium",
  hero2: "text-4xl font-medium  lg:text-[42px] lg:leading-[56px] md:text-5xl",
  h1: "text-4xl font-medium  lg:text-[42px] lg:leading-[56px] md:text-5xl ",
  h2: "text-3xl font-medium md:text-4xl",
  h3: "text-2xl font-medium md:text-3xl",
  h4: "text-xl font-medium md:text-2xl",
  h5: "text-lg font-medium md:text-xl",
  h6: "text-base font-medium md:text-lg",
};

export function Heading({
  level = "h1",
  children,
  className,
  ...props
}: HeadingProps) {
  const Component = level === "hero" || level === "hero2" ? "h1" : level;
  const headingClasses = twMerge(
    "font-sans tracking-tight",
    headingStyles[level],
    className
  );

  return (
    <Component className={headingClasses} {...props}>
      {children}
    </Component>
  );
}
