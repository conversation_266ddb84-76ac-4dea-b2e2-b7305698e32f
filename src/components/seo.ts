import { Metadata } from "next";
type SEO = {
  metaTitle: string;
  MetaDescription: string;
  Keywords: string;
  ShareImage: {
    data: {
      attributes: {
        url: string;
      };
    };
  };
};

const generateMetadata = ({
  metaTitle,
  MetaDescription,
  Keywords,
  ShareImage,
}: SEO): Metadata => {
  // Default values in case props are not provided
  const title = metaTitle || "42 Estates - Luxury Villas in Bangalore";
  const description =
    MetaDescription ||
    "Discover luxury villas by 42 Estates in Electronic City and Sarjapur Road, Bangalore";
  const keywords =
    Keywords || "luxury villas, electronic city, sarjapur road, bangalore";
  const image = ShareImage?.data?.attributes?.url
    ? `${process.env.NEXT_PUBLIC_STRAPI_URL}${ShareImage.data.attributes.url}`
    : "/logo.svg";

  return {
    title,
    description,
    keywords: keywords || undefined,
    metadataBase: new URL("https://42estates.com"), // Replace with your domain
    openGraph: {
      title,
      description,
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [image],
    },
    robots: {
      index: true,
      follow: true,
    },
    alternates: {
      canonical: "/",
    },
  };
};

export default generateMetadata;
