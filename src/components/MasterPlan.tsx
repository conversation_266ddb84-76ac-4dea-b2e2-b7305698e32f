"use client";

import { useState } from "react";
import Image from "next/image";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { ZoomableMasterPlan } from "./ZoomableMasterPlan";

export function MasterPlan({ image }: { image: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div
        className="w-full cursor-zoom-in md:py-8"
        onClick={() => setIsOpen(true)}
        role="button"
        aria-label="Open zoomable master plan"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            setIsOpen(true);
          }
        }}
      >
        <Image
          src={image}
          alt="Master Plan"
          width={1200}
          height={600}
          className="w-full h-auto"
        />
      </div>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-[95vw] w-full max-h-[95vh] h-full p-0">
          <ZoomableMasterPlan image={image} />
        </DialogContent>
      </Dialog>
    </>
  );
}
