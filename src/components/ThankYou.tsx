"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { CheckCircle } from "lucide-react";

export default function ThankYou() {
  const [countdown, setCountdown] = useState(5);
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectUrl = searchParams.get("redirect") || "/";

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (countdown <= 0) {
      router.push(redirectUrl);
    }
  }, [countdown, router, redirectUrl]);

  return (
    <div className="min-h-screen bg-custom-gradient flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full space-y-8 text-center">
        <div className="relative">
          <div className="flex justify-center mb-6">
            <CheckCircle className="w-24 h-24 text-green-500 animate-bounce" />
          </div>
          <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
            <div className="w-32 h-32 bg-green-300 rounded-full opacity-20 animate-ping"></div>
          </div>
        </div>
        
        <h1 className="text-5xl sm:text-6xl font-extrabold text-gray-900 tracking-tight">
          Thank You!
        </h1>
        
        <h2 className="mt-6 text-3xl font-bold text-gray-800 tracking-tight">
          Form submitted successfully
        </h2>
        
        <p className="mt-2 text-lg text-gray-600">
          We&apos;ve received your enquiry and will get back to you shortly!
        </p>
        
        <div className="mt-8 p-4 bg-white/50 rounded-lg border border-gray-200">
          <p className="text-gray-700 font-medium">
            Redirecting back in {countdown} seconds...
          </p>
          <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-1000 ease-linear"
              style={{ width: `${((5 - countdown) / 5) * 100}%` }}
            ></div>
          </div>
        </div>
        
        <div className="mt-6 flex flex-col sm:flex-row justify-center gap-4">
          <Link
            href={redirectUrl}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-secondary-foreground focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
          >
            Go back now
          </Link>
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
          >
            Go to homepage
          </Link>
        </div>
        
        <div className="mt-12 flex justify-center space-x-4">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className={`w-3 h-3 rounded-full bg-green-400 animate-bounce`}
              style={{ animationDelay: `${i * 0.1}s` }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
}
