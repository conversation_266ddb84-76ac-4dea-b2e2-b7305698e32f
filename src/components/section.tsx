import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

import Image from "next/image";

interface SectionProps {
  title?: string;
  description?: string;
  features?: {
    id: string;
    Content: string;
  }[];
  image: string;
  imageAlt?: string;
  imagePosition?: "left" | "right";
  className?: string;
  imgClassName?: string;
  makeSticky?: boolean;
  mobileImagePosition?: "after" | "before";
}

export function Section({
  title,
  description,
  features,
  image,
  imageAlt,
  imagePosition = "right",
  className,
  imgClassName,
  makeSticky = true,
  mobileImagePosition = "before",
}: SectionProps) {
  return (
    <div className={cn("mx-auto container py-0 md:py-12 ", className)}>
      <div className="mx-auto max-w-2xl lg:max-w-none">
        <div className="flex flex-col gap-8 lg:gap-16 lg:flex-row">
          <div
            className={cn(
              "w-full lg:w-1/2",
              mobileImagePosition === "before" ? "order-first" : "order-last",
              imagePosition === "left" ? "md:order-first" : "md:order-last"
            )}
          >
            <div
              className={cn(
                "relative aspect-[4/3] w-full",
                makeSticky ? "lg:sticky lg:top-20" : "",
                imgClassName
              )}
            >
              <Image
                src={image || "/placeholder.svg"}
                alt={imageAlt || title || "Content Image"}
                fill
                sizes="(max-width: 1024px) 100vw, 50vw"
                className={cn("rounded-2xl object-cover", imgClassName)}
              />
            </div>
          </div>
          <div
            className={cn(
              "w-full lg:w-1/2 flex flex-col",
              imagePosition === "left" ? "lg:order-last" : "lg:order-first"
            )}
          >
            {title && (
              <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 md:mb-6">
                {title}
              </h2>
            )}
            {description && (
              <div className="font-normal text-xs leading-[21px] md:text-lg md:leading-8 ">
                {description}
              </div>
            )}
            {features && features.length > 0 && (
              <div className="flex flex-wrap gap-4">
                {features.map((item) => (
                  <Badge
                    key={item.id}
                    variant="secondary"
                    className="text-sm align-middle text-center"
                  >
                    {item.Content}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
