"use client";

import Link from "next/link";
import { useEffect, useState } from "react";

export default function InternalServerError() {
  const [isGlitching, setIsGlitching] = useState(false);

  useEffect(() => {
    const glitchInterval = setInterval(() => {
      setIsGlitching(true);
      setTimeout(() => setIsGlitching(false), 200);
    }, 3000);

    return () => clearInterval(glitchInterval);
  }, []);

  return (
    <div className="min-h-screen bg-custom-gradient flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full space-y-8 text-center">
        <div className="relative">
          <h1
            className={`text-9xl font-extrabold text-red-500 tracking-widest ${
              isGlitching ? "animate-glitch" : ""
            }`}
          >
            500
          </h1>
          <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
            <div className="w-24 h-24 bg-red-500 rounded-full opacity-20 animate-ping"></div>
          </div>
        </div>
        <h2 className="mt-8 text-4xl font-bold text-gray-800 tracking-tight">
          Internal Server Error
        </h2>
        <p className="mt-2 text-lg text-gray-600">
          Oops! Something went wrong on our end. We're working to fix it.
        </p>
        <div className="mt-6 flex flex-col sm:flex-row justify-center gap-4">
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-secondary-foreground focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out"
          >
            Go back home
          </Link>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out"
          >
            Try again
          </button>
        </div>
        <div className="mt-12 flex justify-center space-x-4">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className={`w-3 h-3 rounded-full bg-red-300 animate-bounce`}
              style={{ animationDelay: `${i * 0.1}s` }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
}
