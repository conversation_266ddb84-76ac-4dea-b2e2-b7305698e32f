import Link from "next/link";

export default function ComingSoon() {
  return (
    <div className="min-h-screen bg-custom-gradient flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full space-y-8 text-center">
        <div className="relative">
          <h1 className="text-5xl sm:text-7xl font-extrabold text-gray-900 tracking-tight animate-pulse">
            Coming Soon
          </h1>
          <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
            <div className="w-32 h-32 bg-blue-300 rounded-full opacity-20 animate-ping"></div>
          </div>
        </div>
        <h2 className="mt-6 text-3xl font-bold text-gray-800 tracking-tight">
          Something awesome is in the works!
        </h2>
        <p className="mt-2 text-lg text-gray-600">
          We're working hard to bring you something amazing. Stay tuned!
        </p>
        <div className="mt-10 flex flex-col sm:flex-row justify-center gap-4">
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
          >
            Go back home
          </Link>
          <Link
            href="/contact"
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
          >
            Contact us
          </Link>
        </div>
        <div className="mt-12 flex justify-center space-x-4">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className={`w-3 h-3 rounded-full bg-blue-400 animate-bounce`}
              style={{ animationDelay: `${i * 0.1}s` }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
}
