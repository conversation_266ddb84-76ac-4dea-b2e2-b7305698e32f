import Image from "next/image";
import Link from "next/link";
import { Bath, MapPinned, BedDouble, Grid2X2 } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogHeader,
} from "./ui/dialog";
import ModalForm from "./ModalForm";
import { useState } from "react";

interface ProjectCardProps {
  title: string;
  slug: string;
  location: string;
  status: string;
  image: string;
  context: any;
  category: any;
  specs: {
    beds: number;
    baths: number;
    area: string;
  };
}

export function ProjectCard({
  title,
  slug,
  location,
  status,
  image,
  specs,
  context,
  category,
}: ProjectCardProps) {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <>
      <Card className="overflow-hidden group p-3 md:p-4 flex flex-col">
        <div className="relative aspect-[4/3] rounded-lg overflow-hidden">
          <Image
            src={image || "/logo.svg"}
            alt={title}
            fill
            className="object-cover transition-transform group-hover:scale-105"
          />
          <Badge
            variant="secondary"
            className="absolute rounded-full bottom-2 left-2 md:bottom-4 md:left-4 bg-white hover:bg-white text-xs md:text-[16px] font-light"
          >
            {status}
          </Badge>
        </div>
        <CardContent className="p-0 pt-4 flex-1">
          <Link href={`/projects/${slug}`}>
            <h3 className="text-xl md:text-2xl font-medium group-hover:text-primary transition-colors">
              {title}
            </h3>
          </Link>

          <div className="flex items-center gap-2 mt-2 ">
            <MapPinned size={14} />
            <p className="text-xs md:text-sm font-normal mt-1">{location}</p>
          </div>
        </CardContent>
        {category !== "Commercial" && category !== "Hospitality" && (
          <CardFooter className="p-0 pt-4 flex items-center justify-between h-12 md:h-16">
            <div className="flex items-center gap-1">
              <BedDouble
                strokeWidth={0.75}
                size={32}
                className="w-5 h-5 md:w-8 md:h-8 text-primary"
              />
              <span className="text-[16px] md:text-lg mt-1">{specs.beds}</span>
            </div>
            <div className="w-[1px] bg-primary h-full"></div>
            <div className="flex items-center gap-1">
              <Bath
                strokeWidth={0.75}
                size={32}
                className=" w-5 h-5 md:w-8 md:h-8 text-primary"
              />
              <span className="text-[16px] md:text-lg mt-1">{specs.baths}</span>
            </div>
            <div className="w-[1px] bg-primary h-full"></div>
            <div className="flex items-center gap-1">
              <Grid2X2
                strokeWidth={0.75}
                size={32}
                className="w-5 h-5 md:w-8 md:h-8 text-primary"
              />
              <span className="text-[16px] md:text-lg mt-1">{specs.area}</span>
            </div>
          </CardFooter>
        )}
        <CardFooter className="p-0 pt-4 flex gap-6 items-center justify-between">
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <Button className=" w-full h-[40px] text-xs  md:h-[47px] bg-primary p-4 md:text-sm font-normal ">
                Request Call
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-primary w-[90%] sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle className="text-white">Enquire Now</DialogTitle>
              </DialogHeader>
              <ModalForm
                setIsOpen={setIsOpen}
                isOpen={isOpen}
                context={{ ...context, otherInfo: title }}
              />
            </DialogContent>
          </Dialog>
          <Link href={`/projects/${slug}`} className="w-full">
            <Button className="w-full h-[40px] text-xs  md:h-[47px] bg-secondary p-4 text-black md:text-sm font-normal hover:text-white">
              More Details
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </>
  );
}
