"use client";

import { cn } from "@/lib/utils";

interface FilterTabsProps {
  categories: string[];
  activeCategory: string;
  onChange: (category: string) => void;
}

export function FilterTabs({
  categories,
  activeCategory,
  onChange,
}: FilterTabsProps) {
  return (
    <div className="flex flex-nowrap overflow-auto gap-2">
      {categories.map((category) => (
        <button
          key={category}
          onClick={() => onChange(category)}
          className={cn(
            "px-2 md:px-6 py-1 md:py-2 rounded-full text-[10px] md:text-2xl transition-colors",
            activeCategory === category
              ? "bg-primary font-bold text-primary-foreground"
              : "bg-white font-normal hover:bg-muted/80"
          )}
        >
          {category}
        </button>
      ))}
    </div>
  );
}
