"use client";

import React from "react";
import Image from "next/image";
import useEmblaCarousel from "embla-carousel-react";
import AutoPlay from "embla-carousel-autoplay";

interface DualSliderProps {
  images: string[];
  speed?: number;
  autoplaySpeed?: number;
}

export default function DualSlider({ images }: DualSliderProps) {
  // const [mounted, setMounted] = useState(false);

  const midIndex =
    images.length > 3 ? Math.ceil(images.length / 2) : images.length;
  const topImages = images.slice(0, midIndex);
  const bottomImages = images.length > 3 ? images.slice(midIndex) : [];

  console.log(topImages, bottomImages);

  const autoplayOptions = {
    slidesToScroll: 1,
    loop: true,
    stopOnInteraction: false,

    // rootNode: (emblaRoot: any) => emblaRoot.parentElement,
  };

  const [topEmblaRef] = useEmblaCarousel(
    {
      direction: "ltr",
      loop: true,
      dragFree: true,
      containScroll: "trimSnaps",
      align: "center",
    },
    [AutoPlay(autoplayOptions)]
  );

  const [bottomEmblaRef] = useEmblaCarousel(
    {
      direction: "rtl",
      loop: true,
      dragFree: true,
      align: "center",
      containScroll: "trimSnaps",
    },
    [AutoPlay({ ...autoplayOptions })]
  );

  return (
    <div className="w-full space-y-4">
      {/* Top Slider */}
      <section className="-mx-4">
        <div className="overflow-hidden" key="1" ref={topEmblaRef}>
          <div className="flex">
            {topImages.map((image, index) => (
              <div
                key={index}
                className="flex-[0_0_70%] min-w-0 px-2 sm:flex-[0_0_50%] md:flex-[0_0_33.33%]"
              >
                <div className="relative w-full h-[136px] md:h-[250px] overflow-hidden">
                  <Image
                    src={image}
                    alt={`Top Slide ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {images.length > 6 ? (
        <section className="-mx-4">
          <div
            className="overflow-hidden"
            dir="rtl"
            key="2"
            ref={bottomEmblaRef}
          >
            <div className="flex">
              {bottomImages.map((image, index) => (
                <div
                  key={index}
                  className="flex-[0_0_70%] min-w-0 px-2 sm:flex-[0_0_50%] md:flex-[0_0_33.33%]"
                >
                  <div className="relative w-full h-[136px] md:h-[250px] overflow-hidden">
                    <Image
                      src={image}
                      alt={`Bottom Slide ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      ) : null}
    </div>
  );
}
