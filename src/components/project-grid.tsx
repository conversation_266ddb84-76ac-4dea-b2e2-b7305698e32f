"use client";

import { useState } from "react";
import { FilterTabs } from "./filter-tabs";
import { ProjectCard } from "./project-card";

interface Project {
  id: number;
  attributes: {
    title: string;
    Slug: string;
    location: string;
    Sell_Do_Form: string;
    status: {
      data: {
        attributes: {
          name: string;
        };
      };
    };
    image: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
    category: {
      data: {
        attributes: {
          name: string;
        };
      };
    };
    sftarea: string;
  };
}

interface ProjectGridProps {
  projects: {
    data: Project[];
  };
}

export function ProjectGrid({ projects }: ProjectGridProps) {
  const [activeCategory, setActiveCategory] = useState("All Projects");

  const categories = [
    "All Projects",
    "Residential",
    "Commercial",
    "Hospitality",
  ];

  // Define sorting priority
  const statusOrder: Record<string, number> = {
    "on going": 1,
    upcoming: 2,
    completed: 3,
  };

  // Filter and sort projects
  const filteredProjects = projects?.data
    ?.filter((project) => {
      if (activeCategory === "All Projects") return true;
      return (
        project.attributes.category.data.attributes.name === activeCategory
      );
    })
    .sort((a, b) => {
      const statusA = a.attributes.status.data.attributes.name.toLowerCase();
      const statusB = b.attributes.status.data.attributes.name.toLowerCase();

      const statusComparison =
        (statusOrder[statusA] || 99) - (statusOrder[statusB] || 99);

      if (statusComparison !== 0) return statusComparison;

      // Sort by publishedAt in descending order
      return (
        new Date(b.attributes.publishedAt).getTime() -
        new Date(a.attributes.publishedAt).getTime()
      );
    });

  return (
    <div className="container mx-auto px-4">
      <FilterTabs
        categories={categories}
        activeCategory={activeCategory}
        onChange={setActiveCategory}
      />
      <div className="h-4 md:h-8 w-full"> </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProjects.map((project) => (
          <ProjectCard
            key={project.id}
            title={project.attributes.title}
            slug={project.attributes.Slug}
            location={project.attributes.location}
            status={project.attributes.status.data.attributes.name.toLowerCase()}
            image={
              process.env.NEXT_PUBLIC_STRAPI_URL +
                project.attributes.image.data?.attributes.url || ""
            }
            context={{ projectId: project.attributes.Sell_Do_Form, srd: "" }}
            specs={{
              beds: project.attributes.bhk || "-",
              baths: project.attributes.bath || "-",
              area: project.attributes.sftarea || "-",
            }}
            category={project.attributes.category.data.attributes.name}
          />
        ))}
      </div>
    </div>
  );
}
