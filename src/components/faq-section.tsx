import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export default function FAQSection({ faqs }) {
  return (
    <div className="w-full max-w-3xl mx-auto">
      <Accordion type="single" collapsible className="w-full">
        {faqs.data.map((faq) => (
          <AccordionItem key={faq.id} value={faq.id}>
            <AccordionTrigger className="text-sm md:text-2xl font-medium ">
              {faq.attributes.Questions}
            </AccordionTrigger>
            <AccordionContent className="text-[13.56px] leading-[20.34px] md:text-[16px] font-normal md:leading-7">
              {faq.attributes.Answers}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}
