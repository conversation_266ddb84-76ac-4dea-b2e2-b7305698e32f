import Image from "next/image";
import Link from "next/link";
import {
  Facebook,
  Instagram,
  Linkedin,
  Twitter,
  Youtube,
  Phone,
  Mail,
  MapPinned,
  X,
} from "lucide-react";
import { CustomTwitterXIcon } from "./twitterxicon";

export default function Footer() {
  return (
    <footer className="bg-[#323232] relative text-white">
      <div className="container mx-auto px-4">
        {/* RERA Number */}
        <div className="flex flex-col md:flex-row md:items-center gap-4">
          <Link href="/" className=" w-24 h-24 rounded-lg">
            <Image src="/logo.svg" alt="Logo" width={80} height={80} priority />
          </Link>
          <p className="text-xs md:text-sm">
            RERA No : PRM/KA/RERA/1251/310/AG/180430/000875
          </p>
        </div>
        <div className="md:h-6 w-full"></div>
        <div className="h-[1px] w-full bg-[#727272] hidden md:block "></div>
        <div className="h-6 md:h-12 w-full"></div>
        {/* Main Footer Content */}
        <div className="grid grid-cols-2 md:grid-cols-6 gap-8 mb-8">
          {/* Quick Links */}
          <div>
            <h3 className="text-lg md:text-xl font-bold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/about"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="/projects"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  Our projects
                </Link>
              </li>

              <li>
                <Link
                  href="/careers"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  Careers
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  Reach Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Hospitality */}
          <div>
            <h3 className="text-lg md:text-xl font-bold mb-4">Hospitality</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/projects/42-cardamom-roots"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  42 Cardamom Roots
                </Link>
              </li>
              <li>
                <Link
                  href="/projects/42-Green-Pastures"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  42 Green Pastures
                </Link>
              </li>
              <li>
                <Link
                  href="/projects/Iris-Hotel"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  Iris Hotel
                </Link>
              </li>
            </ul>
          </div>

          <div className="h-[1px] w-full bg-[#727272] col-span-2 md:hidden"></div>

          {/* Important links */}
          <div>
            <h3 className="text-lg md:text-xl font-bold mb-4">
              Important links
            </h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/joint-venture"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  Joint venture
                </Link>
              </li>
              <li>
                <Link
                  href="/privacy-policy"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  Privacy policy
                </Link>
              </li>
              <li>
                <Link
                  href="/blogs"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  Blogs
                </Link>
              </li>
              <li>
                <Link
                  href="/faq"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  Faq
                </Link>
              </li>
            </ul>
          </div>

          {/* Locations */}
          <div>
            <h3 className="text-lg md:text-xl font-bold mb-4">Locations</h3>
            <ul className="space-y-2">
              <li>
                <span className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white">
                  Indiranagar
                </span>
              </li>
              <li>
                <span className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white">
                  Sarjapur
                </span>
              </li>
              <li>
                <span className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white">
                  Whitefield
                </span>
              </li>
              <li>
                <span className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white">
                  Varthur
                </span>
              </li>
              <li>
                <span className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white">
                  Electronic city
                </span>
              </li>
              <li>
                <span className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white">
                  Kannur
                </span>
              </li>
              <li>
                <span className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white">
                  Ooty
                </span>
              </li>
            </ul>
          </div>
          <div className="h-[1px] w-full bg-[#727272] col-span-2 md:hidden"></div>
          {/* Contact us */}
          <div className="col-span-2 ">
            <h3 className="text-lg md:text-xl font-bold mb-4">Contact us</h3>
            <div className="space-y-4">
              <div className="flex  gap-2">
                <MapPinned size={24} className="flex-shrink-0 text-primary" />
                <Link
                  href="/contact"
                  className="text-[16px] md:text-lg md:leading-[26px] font-normal text-gray-300 hover:text-white break-words"
                >
                  Forty Two Estates, No.775, 42 High Street,100 ft road, HAL 2nd
                  Stage, Indiranagar, Bangalore, Karnataka - 560038
                </Link>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="w-5 h-5 text-blue-400" />
                <Link
                  href="tel:+918880804242"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  +91 8880804242
                </Link>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="w-5 h-5 text-blue-400" />
                <Link
                  href="mailto:<EMAIL>"
                  className="text-[16px] md:text-lg font-normal text-gray-300 hover:text-white"
                >
                  <EMAIL>
                </Link>
              </div>
              <div className="h-[1px] w-full bg-[#727272] col-span-2 md:hidden"></div>
              {/* Social Media Icons */}
              <div className="justify-center flex gap-4 pt-4 md:justify-start">
                <Link
                  aria-label="Our Instagram"
                  href="https://www.instagram.com/42estates/"
                  className="text-gray-300 hover:text-white"
                >
                  <Instagram className="w-6 h-6" />
                </Link>
                <Link
                  aria-label="Our Facebook"
                  href="https://www.facebook.com/42Estates/"
                  className="text-gray-300 hover:text-white"
                >
                  <Facebook className="w-6 h-6" />
                </Link>
                <Link
                  aria-label="Our Linkedin"
                  href="https://www.linkedin.com/company/42-estates/"
                  className="text-gray-300 hover:text-white"
                >
                  <Linkedin className="w-6 h-6" />
                </Link>
                <Link
                  aria-label="Our X account"
                  href="https://x.com/42Estates"
                  className="text-gray-300 hover:text-white"
                >
                  <CustomTwitterXIcon className="w-6 h-6" />
                </Link>
                <Link
                  aria-label="Our Youtube"
                  href="https://www.youtube.com/channel/UCJce9gdEB3i7Is6zYMtdIrQ"
                  className="text-gray-300 hover:text-white"
                >
                  <Youtube className="w-6 h-6" />
                </Link>
              </div>
            </div>
          </div>
        </div>
        <div className="h-[1px] w-full bg-[#727272] "></div>
        {/* Copyright */}
        <div className="py-4 text-center">
          <p className="text-gray-400 text-sm">
            Copyright © {new Date().getFullYear()}. All Rights Reserved By 42
            Estates
          </p>
        </div>
      </div>
    </footer>
  );
}
