import Link from "next/link";

export default function NotFound() {
  return (
    <div className="min-h-screen bg-custom-gradient flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full space-y-8 text-center">
        <div className="relative">
          <h1 className="text-9xl font-extrabold text-gray-900 tracking-widest">
            404
          </h1>
          <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
            <div className="w-24 h-24 bg-primary rounded-full opacity-20 animate-ping"></div>
          </div>
        </div>
        <h2 className="mt-8 text-4xl font-bold text-gray-800 tracking-tight">
          Whoops! Page not found.
        </h2>
        <p className="mt-2 text-lg text-gray-600">
          The page you're looking for doesn't exist or has been moved.
        </p>
        <div className="mt-6">
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-secondary-foreground focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
          >
            Go back home
          </Link>
        </div>
        <div className="mt-12 flex justify-center space-x-4">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className={`w-3 h-3 rounded-full bg-gray-300 animate-bounce`}
              style={{ animationDelay: `${i * 0.1}s` }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
}
