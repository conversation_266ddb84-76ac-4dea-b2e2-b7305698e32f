"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import Link from "next/link";
import { useState } from "react";
import { z } from "zod";
import ModalForm from "./ModalForm";

interface ContextInfo {
  srd?: string;
  projectId?: string;
  otherInfo?: string;
}

const MobileFloatingBar = ({ srd, projectId, otherInfo }: ContextInfo) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="md:hidden fixed z-20 bottom-0 w-full h-[55px]">
      <div className="flex justify-between text-center bg-primary p-2 gap-2 h-full w-full">
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              aria-label="Enquire Now"
              className="bg-secondary text-sm text-primary w-full"
            >
              Enquire Now
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-primary w-[90%] sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle className="text-white">Enquire Now</DialogTitle>
            </DialogHeader>
            <ModalForm
              setIsOpen={setIsOpen}
              isOpen={isOpen}
              context={{ srd, projectId, otherInfo }}
            />
          </DialogContent>
        </Dialog>

        <Link href="tel:+918880804242" className="w-full">
          <Button
            aria-label="Call Us Now"
            variant="outline"
            className="text-sm text-primary bg-secondary w-full"
          >
            Call Now
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default MobileFloatingBar;
