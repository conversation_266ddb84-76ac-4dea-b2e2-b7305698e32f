import Image from "next/image";

interface BannerProps {
  size?: "small" | "medium" | "large";
  img?: string;
  content?: React.ReactNode | (() => React.ReactNode);
}

export function Banner({ size = "medium", img, content }: BannerProps) {
  // Height mapping based on size prop
  const heightClasses = {
    small: "min-h-[300px] md:min-h-[400px] lg:min-h-[450px]",
    medium: "min-h-[400px] md:min-h-[472px] lg:min-h-[520px]",
    large: "min-h-[500px] md:min-h-[600px] lg:min-h-[680px]",
  };

  // Determine content rendering
  const renderContent = typeof content === "function" ? content() : content;

  return (
    <div className="container mx-auto relative z-10 md:px-4">
      <div className="md:rounded-3xl overflow-hidden bg-custom-gradient">
        <div className="grid lg:grid-cols-2 items-center">
          <div className={`relative w-full h-full ${heightClasses[size]}`}>
            <div className="md:hidden absolute top-0 left-0 bg-black bg-opacity-25 h-full w-full z-10"></div>
            <Image
              src={img || "/projects.webp"}
              alt="Modern residential buildings with distinctive architecture"
              fill
              className="object-center object-cover md:object-center"
              priority
              quality={100}
              loading="eager"
              // sizes="(max-width: 480px) 300px, (max-width: 768px) 480px, (max-width: 1024px) 768px, 100vw"
            />
          </div>
          <div className="p-4 md:p-8 lg:p-16 space-y-4">{renderContent}</div>
        </div>
      </div>
    </div>
  );
}
