import React from "react";

interface SpacerProps {
  size?: "sm" | "md" | "lg" | "xl" | number; // predefined sizes or custom
  axis?: "vertical" | "horizontal"; // spacing direction
}

const Spacer: React.FC<SpacerProps> = ({ size = "md", axis = "vertical" }) => {
  const sizes = {
    sm: "4", // 1rem (Tailwind spacing scale)
    md: "8", // 2rem
    lg: "12", // 3rem
    xl: "16", // 4rem
  };

  const spacing = typeof size === "number" ? size : sizes[size];

  const className =
    axis === "vertical" ? `h-${spacing} w-full` : `w-${spacing} h-full`;

  return <div className={className} aria-hidden="true" />;
};

export default Spacer;
