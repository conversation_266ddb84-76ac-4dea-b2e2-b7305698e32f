"use client";

import Image from "next/image";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface FloorImage {
  id: number;
  floor: string;
  Image: {
    data: {
      attributes: {
        url: string;
        width: number;
        height: number;
      };
    };
  };
}

interface HouseType {
  id: number;
  type: string;
  FloorImage: FloorImage[];
}

interface FloorPlanViewerProps {
  types: HouseType[];
}

export default function FloorPlanViewer({ types }: FloorPlanViewerProps) {
  const [selectedHouseType, setSelectedHouseType] = useState(
    types[0].type.toLowerCase()
  );

  const sortFloors = (floors: FloorImage[]) => {
    return floors.sort((a, b) => {
      if (a.floor.includes("G")) return -1;
      if (b.floor.includes("G")) return 1;
      return parseInt(a.floor) - parseInt(b.floor);
    });
  };

  return (
    <div className="container mx-auto  px-4 md:py-8">
      <Tabs
        value={selectedHouseType}
        onValueChange={setSelectedHouseType}
        className="w-full"
      >
        <TabsList className="mb-6 flex -mx-4  h-[41px] md:h-[62px]  md:max-w-[1000px] justify-start md:justify-center   gap-2 border md:mx-auto border-primary rounded-none bg-white overflow-x-auto overflow-y-hidden no-scrollbar">
          {types.map((type) => (
            <TabsTrigger
              key={type.id}
              value={type.type.toLowerCase()}
              className="text-sm  px-6 py-2 h-[41px] md:h-[62px] md:text-lg bg-white shadow-none data-[state=active]:text-primary data-[state=active]:shadow-none data-[state=active]:rounded-none data-[state=active]:border-b-4 data-[state=active]:border-primary text-[#757575]"
            >
              {type.type}
            </TabsTrigger>
          ))}
        </TabsList>

        {types.map((type) => (
          <TabsContent
            key={type.id}
            value={type.type.toLowerCase()}
            className="mt-0"
          >
            <Tabs
              defaultValue={sortFloors(type.FloorImage)[0].floor}
              className="w-full"
            >
              <TabsList className="mb-6 flex h-auto justify-start md:justify-center gap-2 rounded-none bg-white overflow-x-auto overflow-y-hidden">
                {sortFloors(type.FloorImage).map((floor) => (
                  <TabsTrigger
                    key={floor.id}
                    value={floor.floor}
                    className="text-xs md:text-[16px] px-6 py-2 rounded-full bg-secondary text-black data-[state=active]:bg-primary data-[state=active]:text-white data-[state=active]:shadow-none"
                  >
                    {floor.floor}
                  </TabsTrigger>
                ))}
              </TabsList>

              {sortFloors(type.FloorImage).map((floor) => (
                <TabsContent
                  key={floor.id}
                  value={floor.floor}
                  className="mt-0"
                >
                  <div className="relative mx-auto aspect-[4/3] max-w-3xl overflow-hidden ">
                    <Image
                      src={
                        process.env.NEXT_PUBLIC_STRAPI_URL +
                        floor.Image.data.attributes.url
                      }
                      alt={`${type.type} ${floor.floor} Floor Plan`}
                      className="object-contain"
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
