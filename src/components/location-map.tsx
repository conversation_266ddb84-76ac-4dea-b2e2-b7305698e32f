interface LocationMapProps {
  src: string;
  title?: string;
}

export default function LocationMap({
  src,
  title = "Location Map",
}: LocationMapProps) {
  // Extract the iframe src from the provided string if needed
  const mapSrc = src.includes('src="')
    ? src.split('src="')[1].split('"')[0]
    : src;

  return (
    <div>
      <h2 className="text-[28px] text-center leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 lg:mb-6">
        Navigate with Ease
      </h2>
      <div className="md:h-10 w-full"></div>

      <div className="relative w-full h-[440px] md:pt-[33.25%]">
        <iframe
          title={title}
          src={mapSrc}
          className="absolute inset-0 h-full w-full border-0"
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
        />
      </div>
    </div>
  );
}
