"use client";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";

import DeskTopFormBanner from "./desktop-form-banner";

interface ContextInfo {
  srd?: string;
  projectId?: string;
  otherInfo?: string;
}

const ExpandableEnquiryForm = ({ srd, projectId, otherInfo }: ContextInfo) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleForm = () => setIsExpanded(!isExpanded);

  return (
    <div className="hidden md:block fixed z-20 bottom-0 w-full">
      <button
        onClick={toggleForm}
        className="absolute -top-10 right-8 bg-primary text-white p-2 rounded-t-lg flex items-center gap-2 hover:bg-primary/90 transition-colors"
      >
        <span>Enquire Now</span>
        {isExpanded ? (
          <ChevronDown className="w-4 h-4" />
        ) : (
          <ChevronUp className="w-4 h-4" />
        )}
      </button>

      <div
        className={`
          flex flex-col items-center justify-center gap-4
          bg-primary w-full transition-all duration-300 ease-in-out
          ${isExpanded ? "h-auto py-4 opacity-100" : "h-0 opacity-0"}
          overflow-hidden
        `}
      >
        <DeskTopFormBanner
          isExpanded={isExpanded}
          setIsExpanded={setIsExpanded}
          context={{ projectId: projectId, srd: srd, otherInfo: otherInfo }}
        />
      </div>
    </div>
  );
};

export default ExpandableEnquiryForm;
