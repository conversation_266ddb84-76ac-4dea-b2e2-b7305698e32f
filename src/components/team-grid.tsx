"use client";

import Image from "next/image";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  // SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { useState } from "react";
import parser from "html-react-parser";

export default function TeamGrid({ data }: { data: CorporateProfile }) {
  const managements = data?.attributes?.managements;

  const [selectedMember, setSelectedMember] =
    useState<ManagementAttributes | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const handleMemberClick = (member: ManagementAttributes) => {
    setSelectedMember(member);
    setIsOpen(true);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {managements?.data?.map((member) => (
          <div
            key={member?.id}
            className="relative rounded-3xl overflow-hidden bg-white shadow-lg group cursor-pointer"
            onClick={() => handleMemberClick(member?.attributes)}
          >
            <div className="aspect-[3/4]">
              <Image
                src={
                  process.env.NEXT_PUBLIC_STRAPI_URL +
                  member.attributes?.Image?.data?.attributes?.url
                }
                alt={member?.attributes?.Name}
                fill
                className="object-cover object-end"
              />
            </div>
            <div className="absolute bottom-0 rounded-2xl m-4 left-0 right-0 bg-white p-3 flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-lg">
                  {member?.attributes?.Name}
                </h3>
                <p>{member?.attributes?.position}</p>
              </div>
              <Button
                size="icon"
                variant="secondary"
                className="rounded-full bg-primary hover:bg-primary text-white"
              >
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent
          side="right"
          className="w-[100vw] max-w-[100vw]   md:max-w-[40vw] overflow-y-auto bg-custom-gradient p-10 md:p-20"
        >
          {selectedMember && (
            <>
              <SheetHeader className=" top-0  z-10">
                {/* <SheetTitle className="hidden">
                  {selectedMember?.Name}- {selectedMember?.position}
                </SheetTitle> */}
                <h3 className="text-2xl md:text-4xl font-medium text-primary">
                  {selectedMember?.Name}
                </h3>
                <h3 className="text-sm md:text-lg font-normal mb-2">
                  {selectedMember?.position}
                </h3>
              </SheetHeader>
              <div className="mt-6 pb-8">
                {/* <div className="aspect-[4/3] relative rounded-lg overflow-hidden mb-4">
                  <Image
                    src={
                      process.env.NEXT_PUBLIC_STRAPI_URL +
                      selectedMember.attributes?.Image?.data?.attributes?.url
                    }
                    alt={selectedMember?.attributes?.name}
                    fill
                    className="object-cover"
                  />
                </div> */}
                {selectedMember?.quotes !== "NA" &&
                  selectedMember?.quotes !== "." && (
                    <h1 className="text-xl md:text-2xl font-normal">
                      {selectedMember?.quotes}
                    </h1>
                  )}
                <div className="h-6 w-full"> </div>

                <p className="text-xs md:text-lg">
                  {parser(selectedMember?.description)}
                </p>
              </div>
            </>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
}
