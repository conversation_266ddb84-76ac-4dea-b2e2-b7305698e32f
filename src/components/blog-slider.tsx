"use client";

import React, { useCallback } from "react";
import Image from "next/image";
import useEmblaCarousel from "embla-carousel-react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";

interface BlogPost {
  id: number;
  attributes: {
    title: string;
    type: string;
    Date: string;
    Description: string;
    Content: string;
    Slug: string;
    Image: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
  };
}

interface BlogSliderProps {
  posts: BlogPost[];
}

export default function BlogSlider({ posts }: BlogSliderProps) {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  return (
    <div className="container relative mx-auto px-4">
      <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] text-center font-medium mb-4 lg:mb-6">
        <span className="text-black">Blogs </span>
      </h2>

      {/* <div className="md:h-10 w-full"></div> */}
      <div className="relative mt-8 px-3 md:px-0 md:-mx-3">
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex ">
            {posts.map((post) => (
              <div
                key={post.id}
                className="flex-[0_0_100%] min-w-0 md:flex-[0_0_50%] lg:flex-[0_0_33.333%] px-3"
              >
                <Link href={`/blogs/${post.attributes.Slug}`}>
                  <div className="relative aspect-[16/10] overflow-hidden rounded-2xl">
                    <Image
                      src={
                        process.env.NEXT_PUBLIC_STRAPI_URL +
                          post.attributes.Image.data.attributes.url || ""
                      }
                      alt={post.attributes.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="mt-4 space-y-1 md:space-y-4">
                    <h3 className="text-lg leading-[21.6px] md:text-[32px] md:leading-[38.4px] font-medium ">
                      {post.attributes.Date}
                    </h3>
                    <h2 className="text-sm leading-[21.8px md:text-2xl font-normal md:leading-8 line-clamp-2">
                      {post.attributes.title}
                    </h2>
                    {/* <p className="text-muted-foreground">
                    {post.attributes.Description}
                  </p> */}
                    <p className="text-sm md:text-lg inline-flex items-center text-primary">
                      Read more
                      <ChevronRight className="ml-1 h-4 w-4" />
                    </p>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </div>

      <>
        <button
          onClick={scrollPrev}
          className="absolute left-0 top-1/2 -translate-y-[1/2] -translate-x-0 md:-translate-x-12"
        >
          <ChevronLeft className="w-8 h-8 md:w-12 md:h-12 text-primary" />
        </button>
        <button
          onClick={scrollNext}
          className="absolute right-0 top-1/2 -translate-y-[1/2] translate-x-0 md:translate-x-12"
        >
          <ChevronRight className="w-8 h-8  md:w-12 md:h-12 text-primary" />
        </button>
      </>
    </div>
  );
}
