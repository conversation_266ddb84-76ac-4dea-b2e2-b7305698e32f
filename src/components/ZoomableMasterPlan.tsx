"use client";

import { useState, useCallback } from "react";
import { TransformWrapper, TransformComponent } from "react-zoom-pan-pinch";
import Image from "next/image";

import { DialogClose } from "@/components/ui/dialog";

export function ZoomableMasterPlan({ image }: { image: string }) {
  const [scale, setScale] = useState(1);

  const handleZoomChange = useCallback((ref) => {
    setScale(ref.state.scale);
  }, []);

  return (
    <TransformWrapper
      initialScale={1}
      minScale={0.5}
      maxScale={4}
      onZoomChange={handleZoomChange}
      centerOnInit={true}
    >
      {({ zoomIn, zoomOut, resetTransform }) => (
        <div className="relative w-full h-full">
          <div className="absolute top-4 left-4 z-10 flex gap-2">
            <button
              onClick={() => zoomIn()}
              className="bg-white text-black px-3 py-1 rounded-md shadow-md"
            >
              Zoom In
            </button>
            <button
              onClick={() => zoomOut()}
              className="bg-white text-black px-3 py-1 rounded-md shadow-md"
            >
              Zoom Out
            </button>
            <button
              onClick={() => resetTransform()}
              className="bg-white text-black px-3 py-1 rounded-md shadow-md"
            >
              Reset
            </button>
          </div>
          <DialogClose className="absolute top-4 right-4 z-10">
            {/* <X className="h-6 w-6" /> */}
          </DialogClose>
          <TransformComponent
            wrapperClass="!w-full !h-full"
            contentClass="!w-full !h-full"
          >
            <div className="w-full h-full flex items-center justify-center">
              <Image
                src={image}
                alt="Zoomable Master Plan"
                width={2400}
                height={1200}
                className="w-auto h-auto max-w-none"
                style={{
                  maxWidth: `${100 / scale}%`,
                  maxHeight: `${100 / scale}%`,
                }}
              />
            </div>
          </TransformComponent>
        </div>
      )}
    </TransformWrapper>
  );
}
