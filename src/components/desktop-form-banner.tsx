"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";
import { countryCodes } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Checkbox } from "./ui/checkbox";
import { Label } from "./ui/label";
import { useSearchParams, useRouter } from "next/navigation";
const formSchema = z.object({
  name: z.string().min(2, { message: "Enter a valid name" }),
  countryCode: z.string().default("91"),
  phone: z.string().min(10, { message: "Enter a valid phone number" }),
  email: z.string().email({ message: "Enter a valid email address" }),
  comments: z.string().optional(),
  acceptTerms: z.boolean().refine((val) => val === true, {
    message: "You must accept the terms and conditions",
  }),
});

type FormValues = z.infer<typeof formSchema>;

function DeskTopFormBanner({
  setIsExpanded,
  context,
}: {
  isExpanded: boolean;
  setIsExpanded: any;
  context?: any;
}) {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<FormValues>({
    mode:"onBlur",
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      countryCode: "91",
      phone: "",
      email: "",
      comments: "",
      acceptTerms: true,
    },
  });

  const acceptTerms = watch("acceptTerms");

  const searchParams = useSearchParams();

  const srd = searchParams.get("srd");

  const onSubmit = async (data: FormValues) => {
    try {
      const contextInfo = {
        currentUrl: window.location.href,
        referrer: document.referrer || "direct",
        userAgent: navigator.userAgent,
        submittedAt: new Date().toISOString(),
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
      };

      const payload = {
        ...data,
        projectId: context?.projectId,
        srd: srd || context?.srd,
        otherInfo: context?.otherInfo,
        phone: `+${data.countryCode}${data.phone}`,
        contextInfo,
      };

      console.log("Payload", JSON.stringify(payload, null, 2));

      const response = await fetch("/api/submit-enquiry", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) throw new Error("Failed to submit form");

      reset();
      setIsExpanded(false);

      // Redirect to thank you page with current URL as redirect parameter
      const currentUrl = encodeURIComponent(window.location.href);
      router.push(`/thank-you?redirect=${currentUrl}`);
    } catch (error) {
      toast({
        title: "Error submitting form",
        description:
          error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    }
  };

  const InputWithError = ({
    name,
    placeholder,
    type = "text",
  }: {
    name: keyof FormValues;
    placeholder: string;
    type?: string;
  }) => (
    <div className="relative flex-1">
      <Input
        type={type}
        placeholder={placeholder}
        className={`bg-white ${errors[name] ? "pr-10 border-red-500" : ""}`}
        {...register(name)}
      />
      {errors[name] && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <AlertCircle className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-red-500" />
            </TooltipTrigger>
            <TooltipContent className="bg-red-500">
              <p>{errors[name]?.message}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="w-full px-4">
      <div className="flex flex-wrap items-center justify-center gap-4">
        <div className="w-48">
          <InputWithError name="name" placeholder="Name" />
        </div>

        <div className="flex gap-2">
          <Select
            value={watch("countryCode")}
            onValueChange={(value) => setValue("countryCode", value)}
          >
            <SelectTrigger className="w-[100px] bg-white">
              <SelectValue>
                {
                  countryCodes.find((c) => c.code === watch("countryCode"))
                    ?.flag
                }{" "}
                +{watch("countryCode")}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {countryCodes.map((country) => (
                <SelectItem key={country.code} value={country.code}>
                  <span className="flex items-center gap-2">
                    {country.flag} +{country.code}
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <InputWithError name="phone" placeholder="Phone" />
        </div>

        <div className="w-48">
          <InputWithError name="email" placeholder="E-mail" type="email" />
        </div>

        <div className="w-64">
          <Input
            placeholder="Comments"
            className="bg-white"
            {...register("comments")}
          />
        </div>

        <Button type="submit" variant="outline" disabled={isSubmitting}>
          {isSubmitting ? "Submitting..." : "Submit"}
        </Button>
      </div>

      <div className="flex items-center space-x-2 mt-4 justify-center">
        <Checkbox
          id="terms"
          checked={acceptTerms}
          className={`border-white data-[state=checked]:bg-white data-[state=checked]:text-black ${
            errors.acceptTerms ? "border-red-500" : ""
          }`}
          onCheckedChange={(checked) => {
            setValue("acceptTerms", checked as boolean, {
              shouldValidate: true,
            });
          }}
        />
        <Label
          htmlFor="terms"
          className="text-xs text-white font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          I authorize 42 Estates and its representative to contact me with
          updates and notifications via Email, SMS, WhatsApp, and Call. This
          will override the registry on DND / NDNC.
        </Label>
        {errors.acceptTerms && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <AlertCircle className="h-5 w-5 text-red-500" />
              </TooltipTrigger>
              <TooltipContent className="bg-red-500">
                <p>{errors.acceptTerms.message}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </form>
  );
}

export default DeskTopFormBanner;
