import qs from "qs";
import { getStrapiURL } from "@/lib/utils";
// import { getAuthToken } from "./services/get-token";

const baseUrl = getStrapiURL();

async function fetchData(url: string) {
  //   const authToken = await getAuthToken();

  // const headers = {
  //   method: "GET",
  //   headers: {
  //     "Content-Type": "application/json",
  //     //   Authorization: `Bearer ${authToken}`,
  //   },
  // };

  try {
    const response = await fetch(url);
    const data = await response.json();

    return data;
  } catch (error) {
    console.error("Error fetching data:", error);
    throw error;
  }
}

// function sleep(ms: number): Promise<void> {
//   return new Promise((resolve) => setTimeout(resolve, ms));
// }

export async function getAllBlogs() {
  const url = new URL("/api/blogs", baseUrl);
  url.search = qs.stringify({
    populate: "*",
  });

  return await fetchData(url.href);
}

export async function getBlogBySlug(slug: string) {
  const url = new URL("/api/blogs", baseUrl);
  url.search = qs.stringify(
    {
      filters: {
        Slug: {
          $eq: slug,
        },
      },
      populate: {
        Image: true,
        Seo: {
          populate: ["ShareImage"],
        },
      },
    },
    {
      encodeValuesOnly: true,
    }
  );

  return await fetchData(url.href);
}

export async function getHomePageData() {
  // await sleep(5000);
  const url = new URL("/api/home", baseUrl);

  url.search = qs.stringify(
    {
      populate: [
        "Homebanner.Image",
        "Ourapproach_slider.Image",
        "Ourstory",
        "ourvalues",
        "Award.image",
        "clients.image",
        "blogs.Image",
        "seo.ShareImage",
      ],
    },
    { encodeValuesOnly: true }
  );

  return await fetchData(url.href);
}

export async function getProjects() {
  const url = new URL("/api/products", baseUrl);
  url.search = qs.stringify({
    populate: "*",
  });
  return await fetchData(url.href);
}

export async function getAbout() {
  const url = new URL("/api/abouts", baseUrl);

  // Build the query string dynamically
  url.search = qs.stringify(
    {
      // Uncomment if you need to filter by slug
      // filters: {
      //   Slug: {
      //     $eq: "corporate-profile",
      //   },
      // },
      populate: {
        banner: {
          populate: ["BannerImage"],
        },
        cms: {
          populate: ["image"],
        },
        Seo: true,
        managements: {
          populate: ["Image"],
        },
      },
    },
    {
      encodeValuesOnly: true, // Optionally encode values for URL safety
    }
  );

  console.log(url.href); // Logs the full URL for debugging

  return await fetchData(url.href);
}

export async function getFaqs() {
  const url = new URL("/api/faqs", baseUrl);
  return await fetchData(url.href);
}

export async function getProject(slug: string) {
  const url = new URL(`/api/products`, baseUrl);

  // Build the query string dynamically
  url.search = qs.stringify(
    {
      filters: {
        Slug: {
          $eq: slug,
        },
      },
      populate: {
        amenities: {
          populate: ["Image"],
        },
        MasterPlan: {
          populate: ["image"],
        },
        Project_image: {
          populate: ["image"],
        },
        image: {
          populate: ["image"],
        },
        images: {
          populate: ["image"],
        },
        Type: {
          populate: {
            FloorImage: {
              populate: ["Image"],
            },
          },
        },
        videos: {
          populate: ["video"],
        },

        blogs: {
          populate: ["Image"],
        },
        ProjectUpdates: {
          populate: ["Image"],
        },
        DescriptionImage2: {
          populate: ["Image"],
        },
        projectlogo: {
          populate: ["Image"],
        },
        status: true,
        features_list: true,
        Seo: true,
        price: true,
        faqs: true, // Simple population for single-level relations
        category: true, // Simple population
      },
    },
    {
      encodeValuesOnly: true, // Optionally encode values for URL safety
    }
  );

  console.log(url.href);

  // Fetch data using the constructed URL
  return await fetchData(url.href);
}
// export async function getGlobalData() {
//   const url = new URL("/api/global", baseUrl);

//   url.search = qs.stringify({
//     populate: [
//       "header.logoText",
//       "header.ctaButton",
//       "footer.logoText",
//       "footer.socialLink",
//     ],
//   });

//   return await fetchData(url.href);
// }

// export async function getGlobalPageMetadata() {
//   const url = new URL("/api/global", baseUrl);

//   url.search = qs.stringify({
//     fields: ["title", "description"],
//   });

//   return await fetchData(url.href);
// }

// export async function getSummaries(queryString: string, currentPage: number) {
//   const PAGE_SIZE = 4;

//   const query = qs.stringify({
//     sort: ["createdAt:desc"],
//     filters: {
//       $or: [
//         { title: { $containsi: queryString } },
//         { summary: { $containsi: queryString } },
//       ],
//     },
//     pagination: {
//       pageSize: PAGE_SIZE,
//       page: currentPage,
//     },
//   });
//   const url = new URL("/api/summaries", baseUrl);
//   url.search = query;
//   return fetchData(url.href);
// }

// export async function getSummaryById(summaryId: string) {
//   return fetchData(`${baseUrl}/api/summaries/${summaryId}`);
// }
