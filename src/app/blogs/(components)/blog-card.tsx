import { Card, CardContent } from "@/components/ui/card";

import Image from "next/image";
import { ChevronRight } from "lucide-react";
import Link from "next/link";

export default function BlogCard({ post }) {
  return (
    <Link href={`/blogs/${post.attributes.Slug}`}>
      <div
        key={post.id}
        className="flex-[0_0_100%] min-w-0 md:flex-[0_0_50%] lg:flex-[0_0_33.333%] "
      >
        <Card className="h-full">
          <CardContent className="p-0">
            <div className="relative aspect-[16/10] overflow-hidden rounded-t-lg">
              <Image
                src={
                  process.env.NEXT_PUBLIC_STRAPI_URL +
                    post.attributes.Image.data.attributes.url || ""
                }
                alt={post.attributes.title}
                fill
                className="object-cover"
              />
            </div>
            <div className="p-6 space-y-4">
              <h2 className="text-2xl font-medium leading-8">
                {post.attributes.title}
              </h2>
              <p className="text-muted-foreground">
                {post.attributes.Description}
              </p>
              <p className="inline-flex items-center text-primary">
                Read more
                <ChevronRight className="ml-1 h-4 w-4" />
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </Link>
  );
}
