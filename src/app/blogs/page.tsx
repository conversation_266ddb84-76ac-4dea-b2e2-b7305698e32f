import { getAllBlogs } from "@/data/loaders";
import React, { Suspense } from "react";
import BlogCard from "./(components)/blog-card";
import { Banner } from "@/components/banner";
import ExpandableEnquiryForm from "@/components/expandable-form";
import MobileFloatingBar from "@/components/mobile-floating-cta";
import { Metadata } from "next";

export const revalidate = 60;

export const metadata: Metadata = {
  title: "Our Articles - 42 Estates Blog",
  description: "Read the latest articles and insights from 42 Estates about luxury villas, real estate trends, and property investment in Bangalore.",
  keywords: "42 Estates blog, real estate articles, luxury villas, Bangalore property, real estate insights",
  openGraph: {
    title: "Our Articles - 42 Estates Blog",
    description: "Read the latest articles and insights from 42 Estates about luxury villas, real estate trends, and property investment in Bangalore.",
    images: ["/blog.webp"],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Our Articles - 42 Estates Blog",
    description: "Read the latest articles and insights from 42 Estates about luxury villas, real estate trends, and property investment in Bangalore.",
    images: ["/blog.webp"],
  },
};

const Content = () => {
  return <h2 className="text-5xl font-medium mb-4">Our Articles</h2>;
};

async function BlogsPage() {
  const blogs = await getAllBlogs();
  console.log(JSON.stringify(blogs, null, 2));
  return (
    <div className="w-full">
      <Banner img="/blog.webp" content={Content} />

      <div className="h-24 w-full"></div>
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {blogs?.data?.map((blog) => (
            <BlogCard key={blog.id} post={blog} />
          ))}
        </div>
      </div>
      <div className="h-24 w-full"></div>
      <Suspense>
        <ExpandableEnquiryForm />
        <MobileFloatingBar />
      </Suspense>
    </div>
  );
}

export default BlogsPage;
