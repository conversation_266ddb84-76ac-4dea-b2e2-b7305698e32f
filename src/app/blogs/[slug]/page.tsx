import { getBlogBySlug, getAllBlogs } from "@/data/loaders";
import { notFound } from "next/navigation";

import parser from "html-react-parser";

import { Banner } from "@/components/banner";
import ExpandableEnquiryForm from "@/components/expandable-form";
import MobileFloatingBar from "@/components/mobile-floating-cta";
import { Suspense } from "react";

export const revalidate = 60; // Revalidate this page every 60 seconds

export async function generateStaticParams() {
  const blogs = await getAllBlogs();
  return blogs?.data?.map((blog) => ({
    slug: blog.attributes.Slug,
  }));
}

export default async function BlogPost({
  params,
}: {
  params: { slug: string };
}) {
  const { slug } = await Promise.resolve(params);
  const data = await getBlogBySlug(slug);

  const blog = data?.data[0];

  if (!blog) {
    notFound();
  }

  console.log(JSON.stringify(blog, null, 2));

  const {
    title,
    Date: date,
    Content: content,
    Image: image,
    Description,
  } = blog.attributes;

  const Content = () => {
    return (
      <>
        <h2 className="text-5xl font-medium text-left mb-4">{title}</h2>
        <p className="text-lg font-normal leading-8">{Description}</p>
        <div className="h-4 w-full"></div>
        <p>{new Date(date).toLocaleDateString() || date}</p>
      </>
    );
  };

  return (
    <div className="w-full">
      <Banner
        img={process.env.NEXT_PUBLIC_STRAPI_URL + image.data?.attributes?.url}
        content={Content}
      />
      <div className="container mx-auto px-4">
        <div className="h-24 w-full"></div>
        <div className="max-w-5xl">
          <p className="text-lg font-normal leading-8">{parser(content)}</p>
        </div>
        <div className="h-24 w-full"></div>
      </div>
      <Suspense>
        <ExpandableEnquiryForm />
        <MobileFloatingBar />
      </Suspense>
    </div>
  );
}
