import localFont from "next/font/local";
import "./globals.css";

import Footer from "@/components/footer";

import { NavbarWrapper } from "./(components)/navbar-wrapper";

import { Toaster } from "@/components/ui/toaster";
import { GoogleTagManager } from "@next/third-parties/google";
import WhatsAppFAB from "@/components/whatsapp-fab";

// const geistSans = localFont({
//   src: "./fonts/GeistVF.woff",
//   variable: "--font-geist-sans",
//   weight: "100 900",
// });

// const geistMono = localFont({
//   src: "./fonts/GeistMonoVF.woff",
//   variable: "--font-geist-mono",
//   weight: "100 900",
// });

const centraleSans = localFont({
  src: [
    // {
    //   path: "./fonts/CentraleSans-Thin.otf",
    //   weight: "100",
    //   style: "normal",
    // },
    // {
    //   path: "./fonts/CentraleSans-ThinItalic.otf",
    //   weight: "100",
    //   style: "italic",
    // },
    // {
    //   path: "./fonts/CentraleSans-Light.otf",
    //   weight: "300",
    //   style: "normal",
    // },
    // {
    //   path: "./fonts/CentraleSans-LightItalic.otf",
    //   weight: "300",
    //   style: "italic",
    // },
    {
      path: "./fonts/CentraleSans-Regular.otf",
      weight: "400",
      style: "normal",
    },
    // {
    //   path: "./fonts/CentraleSans-RegularItalic.otf",
    //   weight: "400",
    //   style: "italic",
    // },
    {
      path: "./fonts/CentraleSans-Medium.otf",
      weight: "500",
      style: "normal",
    },
    // {
    //   path: "./fonts/CentraleSans-MediumItalic.otf",
    //   weight: "500",
    //   style: "italic",
    // },
    {
      path: "./fonts/CentraleSans-Bold.otf",
      weight: "700",
      style: "normal",
    },
    // {
    //   path: "./fonts/CentraleSans-BoldItalic.otf",
    //   weight: "700",
    //   style: "italic",
    // },
  ],
  variable: "--font-centrale-sans",
});

// export const metadata: Metadata = {
//   title: "42 Estates",
//   description: "Real Estate Developer",
// };

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Check if we're on the home page

  return (
    <html lang="en" className={`${centraleSans.variable} `}>
      <head>
        <meta
          name="google-site-verification"
          content="_lt52XjuOTq6CpiQQTy7qWoC888wAmGw6L6FeHdwvw4"
        />
      </head>
      <body className="font-sans">
        <div className="min-h-screen">
          <NavbarWrapper />

          <main className="mx-auto">{children}</main>
          <Footer />
          <Toaster />
        </div>
        <GoogleTagManager gtmId="GTM-PDML3K6" />
        <WhatsAppFAB />
      </body>
    </html>
  );
}
