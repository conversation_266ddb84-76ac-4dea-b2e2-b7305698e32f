import Image from "next/image";

interface TestimonialCardProps {
  content: string;
  author: string;
}

export function TestimonialCard({ content, author }: TestimonialCardProps) {
  return (
    <div
      className={`bg-secondary flex flex-col justify-between relative rounded-xl p-6 shadow-lg transition-all duration-300 ease-in-out h-[243px] md:h-[380px]`}
    >
      <p
        className="text-xs leading-[21px] md:text-lg mb-4 md:leading-[30px] font-normal line-clamp-6"
        style={{
          display: "-webkit-box",
          WebkitBoxOrient: "vertical",
          overflow: "hidden",
        }}
      >
        {content}
      </p>
      <div className="flex items-center gap-3  justify-between">
        <span className="font-medium text-sm  md:text-2xl">{author}</span>
        <Image
          src={"/qoute.png"}
          width={150}
          height={150}
          className="w-10 md:w-24"
          alt="Quote"
        />
      </div>
    </div>
  );
}
