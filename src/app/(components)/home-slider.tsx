"use client";

import useEmblaCarousel from "embla-carousel-react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useCallback, useEffect, useState } from "react";

const SLIDE_DURATION = 3000; // or whatever duration you want
const TOTAL_STEPS = 50; // reduced from 100
const PROGRESS_INTERVAL = SLIDE_DURATION / TOTAL_STEPS;
const PROGRESS_INCREMENT = 100 / TOTAL_STEPS; // Each step will increment by 2%

export default function HeroSlider({ slides }) {
  const [mainEmblaRef, mainEmblaApi] = useEmblaCarousel({
    axis: "y",
    loop: true,
    dragFree: false,
    duration: 20,
    watchDrag: false,
  });

  const [previewEmblaRef, previewEmblaApi] = useEmblaCarousel({
    axis: "y",
    loop: true,
    dragFree: false,
    duration: 20,
    watchDrag: false,
  });

  const [progress, setProgress] = useState(0);

  const scrollPrev = useCallback(() => {
    if (mainEmblaApi && previewEmblaApi) {
      const currentSlide = mainEmblaApi.selectedScrollSnap();
      const prevSlide = (currentSlide - 1 + slides.length) % slides.length;

      mainEmblaApi.scrollTo(prevSlide);
      previewEmblaApi.scrollTo(prevSlide);
    }
  }, [mainEmblaApi, previewEmblaApi, slides.length]);

  const scrollNext = useCallback(() => {
    if (mainEmblaApi && previewEmblaApi) {
      const currentSlide = mainEmblaApi.selectedScrollSnap();
      const nextSlide = (currentSlide + 1) % slides.length;

      mainEmblaApi.scrollTo(nextSlide);
      previewEmblaApi.scrollTo(nextSlide);
    }
  }, [mainEmblaApi, previewEmblaApi, slides.length]);
  useEffect(() => {
    if (!mainEmblaApi || !previewEmblaApi) return;

    // We'll use fewer steps for shorter durations to prevent update stacking

    const timer = setInterval(() => {
      const currentSlide = mainEmblaApi.selectedScrollSnap();
      const nextSlide = (currentSlide + 1) % slides.length;

      mainEmblaApi.scrollTo(nextSlide);
      previewEmblaApi.scrollTo(nextSlide);

      setProgress(0);
    }, SLIDE_DURATION);

    const progressInterval = setInterval(() => {
      setProgress((prevProgress) => {
        if (prevProgress >= 100) return 0;
        return Math.min(prevProgress + PROGRESS_INCREMENT, 100);
      });
    }, PROGRESS_INTERVAL);

    return () => {
      clearInterval(timer);
      clearInterval(progressInterval);
    };
  }, [mainEmblaApi, previewEmblaApi, slides.length]);

  // console.log(slides);

  return (
    <div className="relative w-full h-screen overflow-hidden">
      <div className="embla h-full" ref={mainEmblaRef}>
        <div className="embla__container h-full">
          {slides.map((slide, index) => (
            <div key={`main-${index}`} className="embla__slide relative h-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_STRAPI_URL}${slide.Image.data.attributes.url}`}
                alt={slide.Title}
                fill
                priority={index === 0}
                loading={index === 0 ? "eager" : "lazy"}
                // placeholder="blur"
                // blurDataURL={`data:image/svg+xml;base64,${toBase64(
                //   shimmer(1024, 720)
                // )}`}
                className="object-cover object-center"
                // sizes="(max-width: 480px) 300px, (max-width: 768px) 480px, (max-width: 1024px) 768px, 1024px"
              />
              <div className="absolute bottom-[60%] md:bottom-auto md:top-1/2 left-1/2 md:left-24 transform -translate-x-1/2 md:translate-x-0 md:-translate-y-1/2 w-[80%] max-w-full ">
                <h1 className="text-[46px] leading-[55.2px] md:text-7xl font-medium md:leading-[86px] text-white max-w-2xl">
                  {slide.Title + " " + slide.Description}
                </h1>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Preview Window */}
      <div className="absolute aspect-square top-2/3 left-1/2 md:left-[79%] -translate-x-1/2  shadow-xl transform md:top-1/2 -translate-y-1/2  w-[80%] max-w-[400px] max-h-[400px] overflow-hidden">
        <div className="embla h-full" ref={previewEmblaRef}>
          <div className="embla__container h-full">
            {slides.map((slide, index) => (
              <div
                key={`preview-${index}`}
                className="embla__slide relative h-full"
              >
                <Image
                  src={`${process.env.NEXT_PUBLIC_STRAPI_URL}${slide.Image.data.attributes.url}`}
                  alt={slide.Title}
                  fill
                  priority={index === 0}
                  loading={index === 0 ? "eager" : "lazy"}
                  // placeholder="blur"
                  // blurDataURL={`data:image/svg+xml;base64,${toBase64(
                  //   shimmer(475, 475)
                  // )}`}
                  className="object-cover object-center"
                  // sizes="(max-width: 480px) 300px, (max-width: 768px) 480px, 480px"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="hero-navigation">
        <button
          onClick={scrollPrev}
          aria-label="previous slide"
          className="bg-white/20 hover:bg-white/30 rounded-full p-2 backdrop-blur-sm transition-colors"
        >
          <ChevronLeft className="w-3 h-3 md:w-6 md:h-6 text-white" />
        </button>

        <div className="w-[200px] h-1 bg-white/30 rounded-full overflow-hidden">
          <div
            className="h-full bg-white transition-all duration-100"
            style={{ width: `${progress}%` }}
          />
        </div>

        <button
          onClick={scrollNext}
          aria-label="next slide"
          className="bg-white/20 hover:bg-white/30 rounded-full p-2 backdrop-blur-sm transition-colors"
        >
          <ChevronRight className="w-3 h-3 md:w-6 md:h-6 text-white" />
        </button>
      </div>
      <style jsx global>{`
        .embla {
          overflow: hidden;
        }
        .embla__container {
          display: flex;
          flex-direction: column;
        }
        .embla__slide {
          flex: 0 0 100%;
          position: relative;
        }
        .embla__slide img {
          max-width: 100%; /* Ensure it doesn't upscale */
          height: auto;
        }
      `}</style>
    </div>
  );
}
