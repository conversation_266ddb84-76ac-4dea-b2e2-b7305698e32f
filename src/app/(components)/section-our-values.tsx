import React from "react";
import ProfessionalCard from "./(our-values)/value-card";

function SectionOurValues({ data }) {
  return (
    <section>
      <div className="container mx-auto px-4">
        <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] text-center font-medium mb-4 lg:mb-6">
          <span className="text-black">Our </span>
          <span className="text-primary">Values</span>
        </h2>

        <div className="h-6 md:h-10 w-full"></div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {data?.map((value: any, index: number) => (
            <ProfessionalCard
              key={index}
              image={`/values/value${index + 1}.png`}
              title={value?.title}
              description={value?.content}
            />
          ))}
        </div>
      </div>
    </section>
  );
}

export default SectionOurValues;
