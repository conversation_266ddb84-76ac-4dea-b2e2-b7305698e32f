"use client";

import { useState, useEffect } from "react";
import { TestimonialCard } from "./(review)/TestimonialCard";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function SectionTestimonials({ data = [] }) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 1024);
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return (
    <div className="container mx-auto px-4 ">
      <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] text-center font-medium mb-4 lg:mb-6">
        <span className="text-black">What our </span>
        <br className="md:hidden" />
        <span className="text-primary">client says about us</span>
      </h2>

      <div className="md:h-16 w-full"></div>
      <div className="relative px-4 md:px-0">
        <Swiper
          modules={[Pagination, Navigation]}
          spaceBetween={-150}
          slidesPerView={1}
          breakpoints={{
            640: { slidesPerView: 1 },
            768: { slidesPerView: 2 },
            1024: { slidesPerView: 3 },
          }}
          centeredSlides={true}
          loop={true}
          allowTouchMove={isMobile}
          navigation={{
            prevEl: ".swiper-prev",
            nextEl: ".swiper-next",
          }}
          className="relative "
          style={{ overflow: "hidden", padding: "40px 20px" }}
          onSlideChange={(swiper) => {
            document
              .querySelectorAll(".swiper-slide")
              .forEach((slide, index) => {
                if (index === swiper.activeIndex) {
                  slide.style.zIndex = 10;
                  slide.style.transform = "scale(1.1)";
                  slide.style.transition =
                    "transform 0.2s ease-in-out, opacity 0.2s ease-in-out";
                  slide.style.opacity = "1";
                } else if (
                  index === swiper.activeIndex + 1 ||
                  index === swiper.activeIndex - 1
                ) {
                  slide.style.zIndex = 1;
                  slide.style.transform = "scale(1)";
                  slide.style.opacity = "1";
                  slide.style.pointerEvents = "auto";
                } else {
                  slide.style.opacity = "0";
                  slide.style.pointerEvents = "none";
                }
              });
          }}
        >
          {data?.map((testimonial) => (
            <SwiperSlide
              key={testimonial.id}
              className="transition-transform transform"
            >
              <TestimonialCard
                author={testimonial?.attributes?.name}
                content={testimonial?.attributes?.description}
              />
            </SwiperSlide>
          ))}
        </Swiper>
        <button className="swiper-prev absolute left-0 top-1/2 -translate-y-[1/2] -translate-x-4 md:-translate-x-12">
          <ChevronLeft className="w-8 h-8 md:w-12 md:h-12 text-primary" />
        </button>
        <button className="swiper-next absolute right-0 top-1/2 -translate-y-[1/2] translate-x-4 md:translate-x-12">
          <ChevronRight className="w-8 h-8 md:w-12 md:h-12 text-primary" />
        </button>
      </div>
    </div>
  );
}
