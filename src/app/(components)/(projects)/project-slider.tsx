"use client";

import Image from "next/image";
import { useState, useCallback, useEffect } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { ChevronLeft, ChevronRight, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";
import { FilterTabs } from "./filter-tabs";
import Link from "next/link";

interface Project {
  id: number;
  attributes: {
    title: string;
    Slug: string;
    location: string;
    status: {
      data: {
        attributes: {
          name: string;
        };
      };
    };
    image: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
    category: {
      data: {
        attributes: {
          name: string;
        };
      };
    };
    sftarea: string;
  };
}

interface ProjectSliderProps {
  projects: {
    data: Project[];
  };
}

export function ProjectSlider({ projects }: ProjectSliderProps) {
  const [activeCategory, setActiveCategory] = useState("All Projects");
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    loop: false,
    dragFree: false,
  });

  const categories = [
    "All Projects",
    "Residential",
    "Commercial",
    "Hospitality",
  ];

  // Define sorting priority
  const statusOrder: Record<string, number> = {
    "on going": 1,
    upcoming: 2,
    completed: 3,
  };

  // Filter and sort projects
  // Filter and sort projects
  const filteredProjects = projects?.data
    ?.filter((project) => {
      if (activeCategory === "All Projects") return true;
      return (
        project.attributes.category.data.attributes.name === activeCategory
      );
    })
    .sort((a, b) => {
      const statusA = a.attributes.status.data.attributes.name.toLowerCase();
      const statusB = b.attributes.status.data.attributes.name.toLowerCase();

      const statusComparison =
        (statusOrder[statusA] || 99) - (statusOrder[statusB] || 99);

      if (statusComparison !== 0) return statusComparison;

      // Sort by publishedAt in descending order
      return (
        new Date(b.attributes.publishedAt).getTime() -
        new Date(a.attributes.publishedAt).getTime()
      );
    });

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  useEffect(() => {
    emblaApi?.scrollTo(0);
  }, [activeCategory]);

  return (
    <div className="container mx-auto px-4">
      <div className="flex justify-between items-center">
        <FilterTabs
          categories={categories}
          activeCategory={activeCategory}
          onChange={setActiveCategory}
        />
        <Link href="/projects" className="hidden md:flex">
          <Button variant="default" className="h-[47px] w-[163px]">
            Know More
          </Button>
        </Link>
      </div>

      <div className="relative mt-8 px-3 md:px-0 md:-mx-3">
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex">
            {filteredProjects.map((project) => (
              <div
                key={project.id}
                className="flex-[0_0_100%] min-w-0 sm:flex-[0_0_50%] lg:flex-[0_0_33.333%] px-3 "
              >
                <Link href={`/projects/${project.attributes.Slug}`}>
                  <div className="relative rounded-2xl overflow-hidden aspect-[4/3]">
                    <Image
                      src={
                        process.env.NEXT_PUBLIC_STRAPI_URL +
                        project.attributes.image.data?.attributes.url
                      }
                      alt={project.attributes.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    />
                  </div>
                  <div className="py-4">
                    <h3 className="text-[20px] leading-[24.76px] md:text-2xl md:leading-[31.78px] font-normal">
                      {project.attributes.title}
                    </h3>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-2 text-primary" />
                      <p className="text-xs leading-[24.76px] md:text-[16px] md:leading-[31.78px] font-normal mt-1">
                        {project.attributes.location}
                      </p>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        </div>
        <button
          aria-label="previous project"
          onClick={scrollPrev}
          className="absolute left-0 top-1/3 -translate-y-[1/2] -translate-x-4 md:-translate-x-12"
        >
          <ChevronLeft className="w-8 h-8 md:w-12 md:h-12 text-primary" />
        </button>
        <button
          aria-label="next project"
          onClick={scrollNext}
          className="absolute right-0 top-1/3 -translate-y-[1/2] translate-x-4 md:translate-x-12"
        >
          <ChevronRight className="w-8 h-8  md:w-12 md:h-12 text-primary" />
        </button>
      </div>
    </div>
  );
}
