import { Button } from "@/components/ui/button";

interface FilterTabsProps {
  categories: string[];
  activeCategory: string;
  onChange: (category: string) => void;
}

export function FilterTabs({
  categories,
  activeCategory,
  onChange,
}: FilterTabsProps) {
  return (
    <div className="flex flex-no-wrap  gap-2 md:gap-4 overflow-auto">
      {categories.map((category) => (
        <Button
          key={category}
          onClick={() => onChange(category)}
          className={`py-2 px-4  text-xs md:text-2xl leading-8 md:px-6 md:py-6  rounded-xl font-normal transition-colors ${
            activeCategory === category
              ? "bg-primary text-white border border-primary"
              : "bg-white text-black hover:text-white border border-primary hover:border-primary"
          }`}
        >
          {category}
        </Button>
      ))}
    </div>
  );
}
