"use client";

import { useCallback } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";

export default function SectionAwardSlider({ data = [] }) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    loop: true,
    skipSnaps: false,
    dragFree: false,
  });

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  return (
    <div className="bg-secondary py-8 md:py-16">
      <div className="h-6 md:h-24 w-full"></div>
      <div className="mx-auto container px-4">
        <h2 className="text-[28px] text-center leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 lg:mb-6">
          <span className="text-black"> Awards & </span>
          <span className="text-primary">Recognitions</span>
        </h2>
        <div className="h-2 md:h-12 w-full"></div>
        <div className="relative">
          <div className="overflow-hidden" ref={emblaRef}>
            <div className="flex  gap-8">
              {data.map((item, index) => (
                <div
                  key={index}
                  className="flex flex-col md:flex-row gap-8 flex-[0_0_100%] min-w-0 md:flex-[0_0_750px] relative items-center justify-center mt-16"
                >
                  <div className="relative w-[290px] h-[290px] max-h-[330px] max-w-[330px] overflow-visible">
                    <div className="absolute bottom-[20%] left-1/2 transform -translate-x-1/2 z-10">
                      <Image
                        src={
                          process.env.NEXT_PUBLIC_STRAPI_URL +
                          item?.image?.data?.attributes?.url
                        }
                        alt={item.title || "Award"}
                        width={100}
                        height={200}
                        className="object-contain"
                      />
                    </div>
                    <div className="absolute bottom-[10%] rounded-2xl h-[60%] w-full bg-section-award-gd"></div>
                  </div>
                  <div className="w-[290px] md:w-[390px]">
                    <h3 className="text-[20px] leading-6 md:text-[32px] font-medium mb-2 md:leading-[38px] ">
                      {item.title}
                    </h3>
                    <p className="font-normal text-xs md:text-lg ">
                      {item.subtitle}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="flex flex-row relative mx-auto items-center justify-center text-center mt-16">
          <ChevronLeft
            size={40} // Set larger size directly
            onClick={scrollPrev}
            aria-label="Previous slide"
            className="text-primary cursor-pointer mx-5"
          />

          <ChevronRight
            size={40} // Set larger size directly
            onClick={scrollNext}
            aria-label="Next slide"
            className="text-primary cursor-pointer mx-5"
          />
        </div>
      </div>
    </div>
  );
}
