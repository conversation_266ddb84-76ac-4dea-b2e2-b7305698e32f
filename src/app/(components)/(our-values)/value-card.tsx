import Image from "next/image";
import { Card } from "@/components/ui/card";

export default function ProfessionalCard({ image, title, description }) {
  return (
    <Card className="group relative w-full max-w-sm overflow-hidden rounded-3xl transition-all duration-300 hover:shadow-lg">
      {/* Image Container with Next.js Image */}
      <div className="relative h-[256px] md:h-[400px] w-full">
        <Image
          src={image}
          alt={title}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          // priority
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-transparent" />

        {/* Title */}
        <h2 className="absolute bottom-4 left-4 text-lg md:text-2xl font-semibold text-white">
          {title}
        </h2>

        {/* Hover Content */}
        <div className="hidden absolute inset-0 md:flex translate-y-[101%] flex-col justify-end bg-black/90 p-6 text-white transition-transform duration-300 ease-in-out group-hover:translate-y-0">
          <h2 className="mb-3 text-lg md:text-3xl font-medium">{title}</h2>
          <p className="mb-4 text-lg leading-6 font-normal text-white">
            {description}
          </p>
        </div>
      </div>
    </Card>
  );
}
