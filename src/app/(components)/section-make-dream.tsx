"use client";
import Image from "next/image";
import React from "react";
import CountUp, { useCountUp } from "react-countup";

function SectionMakeDream() {
  return (
    <section className="relative">
      {/* Hero Banner */}
      <div className="relative h-[450px] md:h-[400px] overflow-visible">
        <div className="absolute inset-0 bg-gradient-to-b md:bg-gradient-to-r from-[#6990CA] to-[#ECF2F5] opacity-80" />
        <div className="relative container  px-4 py-16 md:py-0 mx-auto h-full flex items-start md:items-center">
          <h1 className="text-white text-[28px] leading-[33.6px] md:text-[62px] font-medium max-w-2xl md:leading-[74.4px]">
            Make Your Dream Come True With <br />
            42 Estates
          </h1>
        </div>
        <div className="w-full h-[300px] absolute bottom-[0] md:right-[20px]  md:w-[800px] z-10 md:h-[500px]">
          <Image
            src="/make-dream.png"
            alt="Luxury apartment building"
            fill
            className="object-cover object-center"
            // priority
          />
        </div>
      </div>

      {/* Stats Section */}

      <div className="max-w-[900px] px-4 mx-auto py-10 ">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
          <div className="text-left md:text-left">
            <span className="text-[75px] md:text-[100px] leading-none md:leading-[120px] font-bold text-primary">
              <CountUp end={9} enableScrollSpy /> +
            </span>
            <p className="text-xs md:text-2xl font-normal mt-1">
              Years <br className="hidden md:block" /> Experience
            </p>
          </div>
          <div className="text-left col-span-2 md:text-left">
            <span className="text-[75px]  md:text-[100px] leading-none md:leading-[120px] font-bold text-primary">
              <CountUp end={1100} enableScrollSpy /> +
            </span>
            <p className="text-xs  md:text-2xl font-normal mt-1">
              Satisfied <br className="hidden md:block" /> Clients
            </p>
          </div>
        </div>
        <div className="h-8 md:h-16 w-full"> </div>
        <div className="w-full h-[2px] bg-gradient-to-r from-[#6990CA] to-[rgba(52, 71, 100, 0)] "></div>
        <div className="h-8 md:h-16 w-full"> </div>
        <p className="text-xs leading-[21px] md:text-2xl md:leading-10 font-normal">
          42 Estates enjoy a great reputation for building high-quality homes.
          We use the latest technology and materials to make sure that their
          projects are sophisticated and built to last. We also have a great
          team of workers who are dedicated to their craft with years of
          experience adding to their expertise.
        </p>
      </div>
    </section>
  );
}

export default SectionMakeDream;
