import React from "react";
import parser from "html-react-parser";

import Image from "next/image";
import { Button } from "@/components/ui/button";
import Link from "next/link";

function SectionOurStory({ data }) {
  console.log(data);
  const description =
    data?.content[0]?.Content + "</br></br>" + data?.content[1]?.Content;
  return (
    <section className="bg-custom-gradient py-12">
      <div className={"mx-auto container px-4 py-8 md:py-16"}>
        <div className="mx-auto max-w-2xl lg:max-w-none">
          <div className="flex flex-col gap-8 lg:gap-16 lg:flex-row-reverse">
            <div className="relative w-full lg:w-1/2 order-last lg:order-none">
              <div
                className={"relative aspect-[5/6] lg:sticky lg:top-20 w-full"}
              >
                <Image
                  src={"/our-story.png"}
                  alt={"our-story"}
                  fill
                  sizes="(max-width: 1024px) 100vw, 50vw"
                  className={"rounded-2xl object-cover"}
                />
              </div>
            </div>
            <div className={"w-full lg:w-1/2 flex flex-col lg:order-first"}>
              <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 md:mb-6">
                <span className="text-black">Our </span>
                <span className="text-primary">Story</span>
              </h2>
              <div className="font-normal text-xs leading-[21px] md:text-lg md:leading-8 mb-8">
                {parser(description)}
              </div>
              <Link href="/about">
                <Button className="text-sm">More About us</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default SectionOurStory;
