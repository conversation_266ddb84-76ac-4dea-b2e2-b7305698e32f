"use client";
import { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import Image from "next/image";

interface ApproachCard {
  Content: string;
  Image: {
    data: {
      attributes: {
        url: string;
      };
    };
  };
}

interface ApproachData {
  Ourapproach_description: string;
  Ourapproach_slider: ApproachCard[];
}

const OurApproach = ({ data }: { data: ApproachData }) => {
  const containerRef = useRef(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end center"], // Adjusted offset for complete animation
  });

  // Calculate total cards height for proper scroll range
  const totalCardsHeight = (data?.Ourapproach_slider?.length || 0) * 20;

  return (
    <section ref={containerRef} className="relative h-[300vh]">
      <div className="sticky top-0 flex items-center justify-center bg-white py-20 md:py-0">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 md:gap-16 items-center justify-center">
            {/* Left Side Content */}
            <div className="text-left max-w-[490px] mx-auto lg:mx-0">
              <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 md:mb-6">
                <span className="text-black">Our </span>
                <span className="text-primary">Approach</span>
              </h2>
              <p className="text-xs md:text-2xl font-normal leading-[21px] md:leading-[36px]">
                {data?.Ourapproach_description}
              </p>
            </div>

            {/* Right Side - Stacking Cards */}
            <div className="relative w-full h-[400px] md:h-[500px] lg:h-[600px] flex justify-center items-center bg-[url('/logo-outline.png')] bg-contain bg-center bg-no-repeat">
              <div
                className="relative h-full w-full"
                style={{ marginTop: `-${totalCardsHeight / 2}px` }}
              >
                {data?.Ourapproach_slider.map((card, index) => {
                  // Adjust the range to complete earlier
                  const moveRange = [
                    index / (data.Ourapproach_slider.length + 0.5),
                    (index + 1) / (data.Ourapproach_slider.length + 0.5),
                  ];

                  const translateY = useTransform(
                    scrollYProgress,
                    moveRange,
                    [600, 0]
                  );

                  const stackOffset = index * 20;

                  return (
                    <motion.div
                      key={index}
                      className="absolute w-full"
                      style={{
                        translateY,
                        top: `calc(50% - 150px + ${stackOffset}px)`,
                        zIndex: data.Ourapproach_slider.length + index,
                      }}
                    >
                      <div
                        className="
                        bg-[#ECF0F7] rounded-2xl mx-auto backdrop-blur-3xl
                        shadow-[0_0_43.1px_0_rgba(0,29,71,0.3)]
                        p-4 md:p-5 lg:p-6
                        h-[300px] md:h-[350px] lg:h-[409px]
                        w-[280px] md:w-[320px] lg:w-[386px]
                      "
                      >
                        <div className="flex items-center gap-3 md:gap-4 mb-3 md:mb-4">
                          <div className="relative w-[100px] h-[100px] md:w-[134px] md:h-[134px] ">
                            <Image
                              alt="approach illustration"
                              fill
                              className="object-contain"
                              src={`${process.env.NEXT_PUBLIC_STRAPI_URL}${card.Image.data.attributes.url}`}
                              priority={index === 0}
                            />
                          </div>
                        </div>
                        <div className="h-5 md:h-10" />
                        <p className="font-normal text-sm md:text-lg leading-7 md:leading-8">
                          {card.Content}
                        </p>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OurApproach;
