import React from "react";
import { ProjectSlider } from "./(projects)/project-slider";
import Link from "next/link";
import { Button } from "@/components/ui/button";

function SectionProject({ data }) {
  return (
    <>
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div className="w-full">
            <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] text-center md:text-left font-medium mb-4 lg:mb-6">
              <span className="text-black">Our popular </span>
              <span className="text-primary">Properties</span>
            </h2>
          </div>
          <div className="max-w-xl">
            <p className="text-sm leading-5 text-center md:text-left md:text-2xl font-normal md:leading-10">
              42 Estates enjoy a great reputation for building high-quality
              homes. We use the latest technology
            </p>
          </div>
        </div>

        <div className="hidden md:block w-full h-[2px] bg-gradient-to-r from-[#6990CA] to-[rgba(52, 71, 100, 0)]"></div>
      </div>
      <div className="w-full md:h-16"></div>

      <ProjectSlider projects={data} />
      <div className="container mx-auto px-4 flex justify-center mt-4">
        <Link href="/projects" className="md:hidden">
          <Button variant="default">Know More</Button>
        </Link>
      </div>
    </>
  );
}

export default SectionProject;
