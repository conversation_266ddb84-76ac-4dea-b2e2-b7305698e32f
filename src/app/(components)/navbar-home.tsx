"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

export default function NavBarHome() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden md:flex absolute top-0 left-0 right-0 z-50 border-b-[1.1px] border-white h-[114px] items-center ">
        <div className="container px-4 mx-auto flex justify-between">
          <Link href="/" className=" w-24 h-24 top-0 absolute rounded-lg">
            <Image src="/logo.svg" alt="Logo" width={80} height={80} priority />
          </Link>
          <div></div>
          <div className="flex items-center gap-16">
            <Link href="/" className="text-white text-lg font-normal">
              Home
            </Link>
            <Link href="/about" className="text-white text-lg font-normal">
              About
            </Link>
            <Link href="/projects" className="text-white text-lg font-normal">
              Our projects
            </Link>
            <Link href="/blogs" className="text-white text-lg font-normal">
              Blogs
            </Link>
            <Link href="/contact">
              <Button
                variant="default"
                className="bg-primary text-white text-[16px] font-normal"
              >
                Contact us
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}

      <div className="md:hidden absolute top-0 left-0 right-0 z-50 bg-transparent">
        <div className="container mx-auto flex justify-between items-center px-4 h-[80px]">
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <SheetTrigger asChild>
                <button
                  aria-label="Open Navbar Menu"
                  className="text-white bg-transparent w-[35px] h-[35px] flex items-center justify-center p-0"
                >
                  <Menu size={35} className="w-[35px] h-[35px]" />
                </button>
              </SheetTrigger>
            </SheetTrigger>
            <SheetContent side="left" className="w-[300px] sm:w-[400px]">
              <nav className="flex flex-col gap-4">
                <Link
                  href="/"
                  className="text-black text-lg font-normal"
                  onClick={() => setIsOpen(false)}
                >
                  Home
                </Link>
                <Link
                  href="/about"
                  className="text-black text-lg font-normal"
                  onClick={() => setIsOpen(false)}
                >
                  About
                </Link>
                <Link
                  href="/projects"
                  className="text-black text-lg font-normal"
                  onClick={() => setIsOpen(false)}
                >
                  Our projects
                </Link>
                <Link
                  href="/blogs"
                  className="text-black text-lg font-normal"
                  onClick={() => setIsOpen(false)}
                >
                  Blogs
                </Link>
                <Link href="/contact" onClick={() => setIsOpen(false)}>
                  <Button
                    variant="default"
                    className="bg-primary text-white text-[16px] font-normal w-full"
                  >
                    Contact us
                  </Button>
                </Link>
              </nav>
            </SheetContent>
          </Sheet>
          <Link href="/" className="absolute top-0 left-1/2 -translate-x-1/2">
            <Image src="/logo.svg" alt="Logo" width={64} height={64} priority />
          </Link>
        </div>
      </div>
    </>
  );
}
