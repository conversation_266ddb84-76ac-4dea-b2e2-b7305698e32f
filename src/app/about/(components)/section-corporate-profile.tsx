import React from "react";
import parser from "html-react-parser";
import { Section } from "@/components/section";

import Image from "next/image";

function SectionCorporateProfile({ data }: { data: CorporateProfile }) {
  return (
    <section className="container mx-auto px-4">
      <div className="mx-auto flex items-center justify-between">
        <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 lg:mb-6 md:ml-32">
          {parser(data.attributes.banner.BannerTitle)}
        </h2>
        <Image
          src={
            process.env.NEXT_PUBLIC_STRAPI_URL +
            data.attributes.cms.data[0].attributes.image.data[0].attributes.url
          }
          className="hidden md:block"
          width={300}
          height={400}
          alt="about-small"
        />
      </div>
      <div className="h-4 w-full"> </div>

      <Section
        title={data.attributes.cms.data[0].attributes.title}
        imagePosition="left"
        description={
          parser(data.attributes.cms.data[0].attributes.contents) as string
        }
        image={
          process.env.NEXT_PUBLIC_STRAPI_URL +
          data.attributes.cms.data[0].attributes.image.data[0].attributes.url
        }
        imgClassName="rounded-none aspect-[3.5/4]"
      />
    </section>
  );
}

export default SectionCorporateProfile;
