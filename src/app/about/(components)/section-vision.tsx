import React from "react";
import parser from "html-react-parser";
import { Section } from "@/components/section";

function SectionVision({ data }: { data: CorporateProfile }) {
  return (
    <section className="container mx-auto px-4">
      <section className="md:w-[70%] mx-auto">
        <p className="text-center text-sm md:text-xl mb-4">
          Vision and Mission
        </p>
        <h2 className="text-center  text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 md:mb-6">
          42 Estates Envisions At Developing A Path-Breaking Real Estate Firm
        </h2>
      </section>

      <Section
        title={data.attributes.cms.data[0].attributes.title}
        imagePosition="left"
        description={
          parser(data.attributes.cms.data[0].attributes.contents) as string
        }
        image={
          process.env.NEXT_PUBLIC_STRAPI_URL +
          data.attributes.cms.data[0].attributes.image.data[0].attributes.url
        }
      />
    </section>
  );
}

export default SectionVision;
