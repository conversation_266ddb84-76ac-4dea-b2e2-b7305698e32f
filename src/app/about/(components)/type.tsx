interface CorporateProfile {
  id: number;
  attributes: {
    Slug: string;
    title: string;
    Description: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;

    banner: {
      id: number;
      BannerTitle: string;
      BannerTitle2: string;
      ProjectDetails: string;
      Address: string;
      Mobile: null | string;
      BannerImage: {
        data: {
          id: number;
          attributes: ImageAttributes;
        };
      };
    };
    cms: {
      data: Array<{
        id: number;
        attributes: CMSAttributes;
      }>;
    };
    abouts: {
      data: Array<{
        id: number;
        attributes: AboutAttributes;
      }>;
    };
    Seo: {
      id: number;
      metaTitle: string;
      MetaDescription: string;
      Keywords: null | string;
    };
    managements: {
      data: Array<{
        id: number;
        attributes: ManagementAttributes;
      }>;
    };
  };
}

interface ImageAttributes {
  name: string;
  alternativeText: string | null;
  caption: string | null;
  width: number;
  height: number;
  formats: {
    large?: ImageFormat;
    small?: ImageFormat;
    medium?: ImageFormat;
    thumbnail?: ImageFormat;
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: null | string;
  provider: string;
  provider_metadata: null | any;
  createdAt: string;
  updatedAt: string;
}

interface ImageFormat {
  ext: string;
  url: string;
  hash: string;
  mime: string;
  name: string;
  path: null | string;
  size: number;
  width: number;
  height: number;
}

interface CMSAttributes {
  title: string;
  Description: string;
  contents: string;
  Slug: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  image: {
    data: Array<{
      id: number;
      attributes: ImageAttributes;
    }>;
  };
}

interface AboutAttributes {
  Slug: string;
  title: string;
  Description: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  DesImage: {
    data: {
      id: number;
      attributes: ImageAttributes;
    };
  };
  cms: {
    data: Array<{
      id: number;
      attributes: CMSAttributes;
    }>;
  };
}

interface ManagementAttributes {
  Name: string;
  description: string;
  quotes: string;
  position: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  ShortDescription: string;
  slug: string;
  Image: {
    data: {
      id: number;
      attributes: ImageAttributes;
    };
  };
}
