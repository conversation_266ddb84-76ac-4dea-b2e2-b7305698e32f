import React from "react";
import parser from "html-react-parser";
import { Section } from "@/components/section";

function SectionOurLogo({ data }: { data: CorporateProfile }) {
  return (
    <section className="bg-secondary py-12">
      <section className="mx-auto container px-4 py-8 md:py-16">
        <section>
          <p className="text-center md:text-left text-sm md:text-xl mb-4">
            Our logo
          </p>
          <h2 className="text-center md:text-left text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 md:mb-6">
            {parser(data.attributes.banner.BannerTitle)}
          </h2>
        </section>

        <Section
          imagePosition="right"
          description={
            parser(data.attributes.cms.data[0].attributes.contents) as string
          }
          image={
            process.env.NEXT_PUBLIC_STRAPI_URL +
            data.attributes.cms.data[0].attributes.image.data[0].attributes.url
          }
          imgClassName="rounded aspect-[7/8] md:w-[389px] md:h-[452px] mx-auto"
        />
      </section>
    </section>
  );
}

export default SectionOurLogo;
