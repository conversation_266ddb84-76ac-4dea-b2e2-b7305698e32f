import React from "react";
import parser from "html-react-parser";
import { Section } from "@/components/section";

import { Heading } from "@/components/ui/heading";

function SectionMission({ data }: { data: CorporateProfile }) {
  return (
    <section className="container mx-auto px-4">
      <Heading level="h1" className="flex flex-col">
        <span>{parser(data.attributes.banner.BannerTitle)}</span>
      </Heading>
      <div className="h-16 w-full"> </div>
      <Section
        title={"Our " + data.attributes.cms.data[1].attributes.title}
        imagePosition="right"
        description={
          parser(data.attributes.cms.data[1].attributes.contents) as string
        }
        image={
          process.env.NEXT_PUBLIC_STRAPI_URL +
          data.attributes.cms.data[1].attributes.image.data[0].attributes.url
        }
      />
    </section>
  );
}

export default SectionMission;
