import { Banner } from "@/components/banner";

import { getAbout } from "@/data/loaders";
import SectionCorporateProfile from "./(components)/section-corporate-profile";

import SectionOurLogo from "./(components)/section-our-logo";
import SectionVision from "./(components)/section-vision";
import SectionMission from "./(components)/section-mission";
import SectionTeam from "./(components)/secton-team";
import ExpandableEnquiryForm from "@/components/expandable-form";
import MobileFloatingBar from "@/components/mobile-floating-cta";
import generateMetadata from "@/components/seo";
import { Suspense } from "react";

export const revalidate = 3600;

export async function metadata<Metadata>() {
  const about = (await getAbout()) as About;
  console.log(about);
  const corporateProfile = about.data.find(
    (item) => item.attributes.Slug === "corporate-profile"
  );
  const seoData = corporateProfile?.attributes?.Seo;
  return generateMetadata(seoData);
}

const Content = () => {
  return (
    <div>
      <h1 className="text-[42px] leading-[50.4px] md:text-[52px] lg:text-[52px] lg:leading-[54px] font-mediumn">
        Who we are
      </h1>
      <div className="h-2 md:h-4 w-full"></div>
      <p className=" font-normal text-xs leading-[21px]] md:text-lg md:leading-8">
        42 Estates has been holding the blue ribbon of the real estate crest for
        more than a decade. The three pillars on which our work is built are
        simplicity of use, style, and luxury
      </p>
    </div>
  );
};

export default async function AboutPage() {
  const about = (await getAbout()) as About;

  console.log(about);
  const corporateProfile = about.data.find(
    (item) => item.attributes.Slug === "corporate-profile"
  );
  const ourLogo = about.data.find(
    (item) => item.attributes.Slug === "our-logo"
  );
  const ourVision = about.data.find(
    (item) => item.attributes.Slug === "mission-vision"
  );
  const ourTeam = about.data.find(
    (item) => item.attributes.Slug === "management"
  );

  console.log(corporateProfile);
  return (
    <div>
      <Banner size="medium" content={Content} />

      <div className="h-16 md:h-36 w-full"> </div>

      <SectionCorporateProfile data={corporateProfile} />
      <div className="h-16 md:h-36 w-full"> </div>
      <SectionOurLogo data={ourLogo} />
      <div className="h-16 md:h-36 w-full"> </div>
      <SectionVision data={ourVision} />
      <SectionMission data={ourVision} />
      <div className="h-16 md:h-36 w-full"> </div>
      <SectionTeam data={ourTeam} />
      <div className="h-8 md:h-36 w-full"> </div>
      <Suspense>
        <ExpandableEnquiryForm />
        <MobileFloatingBar />
      </Suspense>
    </div>
  );
}
