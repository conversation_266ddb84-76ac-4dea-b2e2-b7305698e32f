import { getAllBlogs } from "@/data/loaders";
import React, { Suspense } from "react";

import { Banner } from "@/components/banner";
import ExpandableEnquiryForm from "@/components/expandable-form";
import MobileFloatingBar from "@/components/mobile-floating-cta";
import PrivacyPolicy from "./(components)/privacy-policy";

export const revalidate = 60;

const Content = () => {
  return <h2 className="text-5xl font-medium mb-4">Privacy Policy</h2>;
};

async function CareersPage() {
  return (
    <div className="w-full">
      <Banner img="/blog.webp" content={Content} />

      <div className="h-24 w-full"></div>
      <div className="container mx-auto px-4">
        <PrivacyPolicy />
      </div>
      <div className="h-24 w-full"></div>
      <Suspense>
        <ExpandableEnquiryForm />
        <MobileFloatingBar />
      </Suspense>
    </div>
  );
}

export default CareersPage;
