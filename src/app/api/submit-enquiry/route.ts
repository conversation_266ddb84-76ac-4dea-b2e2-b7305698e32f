import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import qs from "qs";

// Schema for context information
const contextInfoSchema = z.object({
  currentUrl: z.string().url().optional(),
  referrer: z.string().optional(),
  userAgent: z.string().optional(),
  submittedAt: z.string().datetime().optional(),
  screenSize: z.string().optional(),
});

// Main form schema with context info
const formSchema = z.object({
  name: z.string().min(2),
  phone: z.string().min(10),
  email: z.string().email(),
  comments: z.string().optional(),
  acceptTerms: z
    .boolean()
    .refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    })
    .optional(),
  projectId: z.string().optional(),
  srd: z.string().optional(),
  otherInfo: z.string().optional(),
  contextInfo: contextInfoSchema,
});

// Type for the validated data
type FormData = z.infer<typeof formSchema>;

interface SellDoParams {
  name: string;
  email: string;
  phone: string;
  noteContent?: string;
  srd?: string;
  projectId?: string;
  otherInfo?: string;
}

function formatContextInfo(
  contextInfo: z.infer<typeof contextInfoSchema>
): string {
  const contextEntries = Object.entries(contextInfo)
    .filter(([_, value]) => value !== undefined)
    .map(([key, value]) => `${key}: ${value}`)
    .join("\n");

  return contextEntries ? `\n\nContext Information:\n${contextEntries}` : "";
}

function constructLeadURL(params: SellDoParams): string {
  const baseUrl = "https://app.sell.do/api/leads/create";

  const queryData = {
    api_key: process.env.SELL_DO_API_KEY,
    sell_do: {
      form: {
        lead: {
          name: params.name,
          phone: params.phone,
          email: params.email,
        },
        note: {
          content: params.noteContent,
        },
      },
      campaign: params.srd ? { srd: params.srd } : "5b3b61ef7c0dac0da1c206e6",
      project_id: params.projectId
        ? { project_id: params.projectId }
        : "5d8740f97c0dac663303e2d1",
    },
  };

  if (params.srd) {
    queryData.sell_do.campaign = {
      srd: params.srd,
    };
  }

  if (params.projectId) {
    queryData.sell_do.project_id = params.projectId;
  }

  return `${baseUrl}?${qs.stringify(queryData)}`;
}

async function submitToSellDo(data: FormData): Promise<Response> {
  const formattedContext = formatContextInfo(data.contextInfo);
  const combinedNoteContent = `${data.comments ?? "No comments provided"}${
    data.otherInfo ? ` 🔹 ${data.otherInfo}` : ""
  } 🔹 ${formattedContext}`;

  const sellDoParams: SellDoParams = {
    name: data.name,
    email: data.email,
    phone: data.phone,
    noteContent: combinedNoteContent,
    projectId: data.projectId,
    srd: data.srd,
    otherInfo: data.otherInfo,
  };

  const url = constructLeadURL(sellDoParams);

  const response = await fetch(url, {
    method: "GET", // SellDo API uses GET for lead creation
    headers: {
      Accept: "application/json",
    },
  });

  console.log(response);

  const responseData = await response.json();

  console.log(responseData);

  if (!response.ok || responseData?.error?.length > 0) {
    throw new Error(
      `SellDo API error: ${response.status} ${
        responseData?.error || response.statusText
      }`
    );
  }

  return response;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    console.log(body);
    // Validate incoming data
    const validatedData: FormData = formSchema.parse(body);

    // throw Error("hello");

    await submitToSellDo(validatedData);

    // Log the submission with context
    console.log("Form submitted with context:", {
      ...validatedData,
      contextInfo: validatedData.contextInfo,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      { message: "Form submitted successfully" },
      { status: 200 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.log(error);
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.error("Form submission error:", errorMessage);

    return NextResponse.json(
      { error: "Failed to submit form", message: errorMessage },
      { status: 500 }
    );
  }
}
