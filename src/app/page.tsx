import { getHomePageData, getProjects } from "@/data/loaders";

import HeroSlider from "./(components)/home-slider";
import OurApproach from "./(components)/section-our-approach";
import SectionOurStory from "./(components)/section-our-story";
import SectionProject from "./(components)/section-project";
import SectionOurValues from "./(components)/section-our-values";
import SectionMakeDream from "./(components)/section-make-dream";
import SectionAwardSlider from "./(components)/section-awards";
import SectionTestimonials from "./(components)/section-review";
import SectionBlog from "./(components)/section-blog";
import ExpandableEnquiryForm from "@/components/expandable-form";
import MobileFloatingBar from "@/components/mobile-floating-cta";
import generateMetadata from "@/components/seo";
import { Suspense } from "react";

// export const dynamic = "force-static";

// Next.js will invalidate the cache when a
// request comes in, at most once every 60 seconds.
export const revalidate = 3600;

// We'll prerender only the params from `generateStaticParams` at build time.
// If a request comes in for a path that hasn't been generated,
// Next.js will server-render the page on-demand.
// export const dynamicParams = true; // or false, to 404 on unknown paths

// Dynamic metadata
export async function metadata<Metadata>() {
  const strapiData = await getHomePageData();
  const seoData = strapiData.data?.attributes?.seo;
  return generateMetadata(seoData);
}

export default async function Home() {
  const strapiData = await getHomePageData();

  const projectData = await getProjects();
  // console.log(strapiData);
  const approach = {
    Ourapproach_title: strapiData?.data?.attributes?.Ourapproach_title,
    Ourapproach_slider: strapiData?.data?.attributes?.Ourapproach_slider,
    Ourapproach_description:
      strapiData?.data?.attributes?.Ourapproach_description,
  };

  const ourstory = {
    ourstory_title: strapiData?.data?.attributes?.ourstory_title,
    content: strapiData?.data?.attributes?.Ourstory,
  };

  const ourValues = strapiData?.data?.attributes?.ourvalues;

  const blogs = strapiData?.data?.attributes?.blogs;

  const awards = strapiData?.data?.attributes?.Award;

  const reivews = strapiData?.data?.attributes?.clients?.data;

  return (
    <>
      {/* <Suspense fallback="loadingg..."> */}
      <HeroSlider slides={strapiData?.data?.attributes?.Homebanner} />
      {/* </Suspense> */}
      <div className="md:h-36 md:w-full"> </div>
      <OurApproach data={approach} />
      <div className="md:h-36 md:w-full"> </div>
      <SectionOurStory data={ourstory} />
      <div className="h-16 md:h-36 w-full"> </div>
      <SectionOurValues data={ourValues} />
      <div className="h-16 md:h-64 w-full"> </div>
      {/* <div className="h-64 w-full"> </div> */}
      <SectionMakeDream />
      <div className="h-8 md:h-36 w-full"> </div>
      <SectionAwardSlider data={awards} />
      <div className="h-16 md:h-36 w-full"> </div>
      <SectionProject data={projectData} />
      <div className="h-16 w-full"> </div>
      <SectionTestimonials data={reivews} />
      <div className="h-8 md:h-36 w-full"> </div>
      <SectionBlog data={blogs.data} />
      <div className="h-8 md:h-36 w-full"> </div>
      <Suspense>
        <ExpandableEnquiryForm />
        <MobileFloatingBar />
      </Suspense>
    </>
  );
}
