"use client";
import { Banner } from "@/components/banner";
import SectionAddress from "./(components)/address-section";
import ContactSection from "./(components)/contact-section";
import SectionEnquire from "./(components)/section-enquire";
import { useState } from "react";

const Content = () => {
  return (
    <div className="py-6 md:py-0">
      <h1 className="text-[42px] leading-[50.4px] md:text-[52px] lg:text-[52px] lg:leading-[54px] font-mediumn">
        Contact us
      </h1>
      <div className="h-2 md:h-4 w-full"></div>
      <p className=" font-normal text-xs leading-[21px]] md:text-lg md:leading-8">
        42 Estates has been holding the blue ribbon of the real estate crest for
        more than a decade. The three pillars on which our work is built are
        simplicity of use, style, and luxury
      </p>
    </div>
  );
};

export default function ContactPage() {
  const [subject, setSubject] = useState("general-enquiries");
  console.log("in contact page", subject);
  return (
    <div>
      <Banner size="medium" content={Content} img="/contact.png" />
      <div className="h-12 md:h-36 w-full" id="contact-form-42"></div>

      <ContactSection subject={subject} />

      <div className="h-12 md:h-36 w-full"></div>
      <SectionAddress />
      <div className="h-12 md:h-36 w-full"></div>
      <SectionEnquire setSubject={setSubject} />
      <div className="h-12 md:h-36 w-full"></div>
      {/* <div className="h-36 w-full"></div> */}
    </div>
  );
}
