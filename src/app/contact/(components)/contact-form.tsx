"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  <PERSON><PERSON><PERSON>P<PERSON>ider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { countryCodes } from "@/lib/utils";
import { useEffect } from "react";
import { useRouter } from "next/navigation";

const formSchema = z.object({
  name: z.string().min(2, { message: "Enter a valid name" }),
  countryCode: z.string().default("91"),
  phone: z.string().min(10, { message: "Enter a valid phone number" }),
  email: z.string().email({ message: "Enter a valid email address" }),
  subject: z.string({
    required_error: "Please select a subject",
  }),
  comments: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function ContactForm({
  subject = "general-enquiries",
}: {
  subject: string;
}) {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,

    formState: { errors, isSubmitting },
  } = useForm<FormValues>({
    mode:'onBlur',
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      countryCode: "91",
      phone: "",
      email: "",
      subject: subject,
      comments: "",
    },
  });

  useEffect(() => {
    if (subject) setValue("subject", subject);
  }, [setValue, subject]);

  const onSubmit = async (data: FormValues) => {
    try {
      const contextInfo = {
        currentUrl: window.location.href,
        referrer: document.referrer || "direct",
        userAgent: navigator.userAgent,
        submittedAt: new Date().toISOString(),
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
      };

      const payload = {
        ...data,
        phone: `+${data.countryCode}${data.phone}`,
        otherInfo: data.subject,
        contextInfo,
      };

      const response = await fetch("/api/submit-enquiry", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      console.log(response);

      if (!response.ok) throw new Error("Failed to submit form");

      reset();

      // Redirect to thank you page with current URL as redirect parameter
      const currentUrl = encodeURIComponent(window.location.href);
      router.push(`/thank-you?redirect=${currentUrl}`);
    } catch (error) {
      toast({
        title: "Error submitting form",
        description:
          error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    }
  };

  const InputWithError = ({
    name,
    label,
    placeholder,
    type = "text",
  }: {
    name: keyof FormValues;
    label: string;
    placeholder: string;
    type?: string;
  }) => (
    <div className="space-y-2">
      <Label htmlFor={name} className="hidden md:block">
        {label}
      </Label>
      <div className="relative">
        <Input
          id={name}
          type={type}
          placeholder={placeholder}
          className={`border-gray-200 ${
            errors[name] ? "pr-10 border-red-500" : ""
          }`}
          {...register(name)}
        />
        {errors[name] && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <AlertCircle className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-red-500" />
              </TooltipTrigger>
              <TooltipContent>
                <p>{errors[name]?.message}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  );

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <InputWithError name="name" label="Name" placeholder="Enter Name*" />

      <div className="space-y-2">
        <Label htmlFor="phone" className="hidden md:block">
          Phone
        </Label>
        <div className="flex gap-2">
          <Select
            value={watch("countryCode")}
            onValueChange={(value) => setValue("countryCode", value)}
          >
            <SelectTrigger className="w-[110px] border-gray-200">
              <SelectValue>
                {
                  countryCodes.find((c) => c.code === watch("countryCode"))
                    ?.flag
                }{" "}
                +{watch("countryCode")}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {countryCodes.map((country) => (
                <SelectItem key={country.code} value={country.code}>
                  <span className="flex items-center gap-2">
                    {country.flag} +{country.code}
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <div className="flex-1 relative">
            <Input
              id="phone"
              type="tel"
              placeholder="Enter phone number*"
              className={`border-gray-200 ${
                errors.phone ? "pr-10 border-red-500" : ""
              }`}
              {...register("phone")}
            />
            {errors.phone && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <AlertCircle className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-red-500" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{errors.phone?.message}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>
      </div>

      <InputWithError
        name="email"
        label="Email"
        placeholder="Enter email id*"
        type="email"
      />

      <div className="space-y-2">
        <Label htmlFor="subject" className="hidden md:block">
          Subject
        </Label>
        <Select
          value={watch("subject")}
          onValueChange={(value) => setValue("subject", value)}
        >
          <SelectTrigger
            className={`border-gray-200 ${
              errors.subject ? "border-red-500" : ""
            }`}
          >
            <SelectValue placeholder="Select subject*" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="general-enquiries">General Enquiries</SelectItem>
            <SelectItem value="referrals">Referrals</SelectItem>
            <SelectItem value="vendor-registration">
              Vendor Registration
            </SelectItem>
            <SelectItem value="joint-ventures">Joint Ventures</SelectItem>
          </SelectContent>
        </Select>
        {errors.subject && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <AlertCircle className="h-5 w-5 text-red-500" />
              </TooltipTrigger>
              <TooltipContent>
                <p>{errors.subject.message}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="comments" className="hidden md:block">
          Message
        </Label>
        <div className="relative">
          <Textarea
            id="comments"
            placeholder="Write your message"
            className={`min-h-[150px] border-gray-200 ${
              errors.comments ? "pr-10 border-red-500" : ""
            }`}
            {...register("comments")}
          />
          {errors.comments && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <AlertCircle className="absolute right-3 top-3 h-5 w-5 text-red-500" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>{errors.comments.message}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>

      <Button type="submit" className="w-32 text-sm" disabled={isSubmitting}>
        {isSubmitting ? "Submitting..." : "Submit"}
      </Button>
    </form>
  );
}
