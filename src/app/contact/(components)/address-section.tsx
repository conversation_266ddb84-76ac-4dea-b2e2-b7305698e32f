import React from "react";
import AddressCard from "./address-card";

const location = [
  {
    flag: "/flag-india.png",
    country: "India (Corporate office)",
    address:
      "No.775, 42 High Street 100 ft road HAL 2nd Stage,Indiranagar Bangalore, Karnataka PIN Code: 560038",
    phone: "************",
    email: "<EMAIL>",
  },

  {
    flag: "/flag-ireland.png",
    country: "Ireland",
    address: "11 Hollywoodrath Rise , Hollystown,  D15 YY2H, Dublin 15",
    phone: "+353-879559840",
    email: "<EMAIL>",
  },
  {
    flag: "/flag-usa.png",
    country: "USA",
    address: "3443 Lapp Lane, Naperville, IL , 60564 USA",
    phone: "+****************",
    email: "<EMAIL>",
  },
  {
    flag: "/flag-india.png",
    country: "India",
    address:
      "42 Green Pastures No. 18/59 42 Green Pastures road. Dundanari Allada Valley Coonoor, Ooty Tamil Nadu: 643213",
    phone: "+91-9442636300",
    email: "<EMAIL>",
  },
  {
    flag: "/flag-dubai.png",
    country: "Dubai",
    address: "Matergram PO BOX 29105 Dubai",
    phone: "+************",
    email: "<EMAIL>",
  },

  {
    flag: "/flag-aus.png",
    country: "Australia",
    address: "1/1263 Toorak road Camberwell Victoria 3124 Melbourne",
    phone: "+61 ***********",
    email: "<EMAIL>",
  },
];

export default function SectionAddress() {
  return (
    <div className="w-full bg-secondary ">
      <div className="container mx-auto px-4 py-8 md:py-24">
        <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] text-center font-medium mb-4 lg:mb-6">
          <span className="text-black">Our presence </span>
        </h2>

        <div className="md:h-8 w-full"></div>
        <div className="grid grid-cols-1 md:grid-cols-3 md:grid-rows-2 gap-10">
          {location.map((item, index) => {
            return <AddressCard key={index} location={item} />;
          })}
        </div>
      </div>
    </div>
  );
}
