import { Mail, Phone } from "lucide-react";
import Image from "next/image";
import React from "react";

type LocationType = {
  flag: string;
  country: string;
  address: string;
  phone: string;
  email: string;
};

function AddressCard({ location }: { location: LocationType }) {
  return (
    <div className="rounded-3xl bg-white  w-full py-6 md:py-10 px-4">
      <div className="flex items-center gap-2">
        <Image alt="nation" src={location.flag} width={50} height={50} />
        <div>
          <h3 className="font-medium text-lg md:text-2xl">
            {location.country}
          </h3>
        </div>
      </div>
      <div className="h-4 w-full"></div>
      <div>
        <p className="font-normal text-sm  md:text-lg text-[#484848] md:leading-8">
          {location.address}
        </p>
      </div>
      <div className="space-y-4">
        <div className="flex items-center gap-3 mt-4">
          <Phone className="text-primary h-4 w-4 md:h-6 md:w-6" />
          <span className="text-sm md:text-lg mt-1 font-medium">
            {location.phone}
          </span>
        </div>
        <div className="flex items-center gap-3 mt-4">
          <Mail className="text-primary h-4 w-4 md:h-6 md:w-6" />
          <span className="text-sm md:text-lg mt-1 font-medium">
            {location.email}
          </span>
        </div>
      </div>
    </div>
  );
}

export default AddressCard;
