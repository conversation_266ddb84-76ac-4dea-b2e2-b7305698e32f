import { Mail, MapPin, Phone } from "lucide-react";
import ContactForm from "./contact-form";

export default function ContactSection({ subject }: { subject: string }) {
  return (
    <div className="container mx-auto px-4">
      <div className="grid gap-10 lg:grid-cols-2">
        <div className="space-y-4">
          <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 md:mb-6">
            <span className="text-black"> We&apos;d love to </span>
            <span className="text-primary">hear from you</span>
          </h2>

          <ContactForm subject={subject} />
        </div>
        <div className="relative overflow-hidden rounded-3xl bg-primary p-12 text-white">
          <div className="absolute right-0 top-0 opacity-20">
            {/* <div className="text-[20rem] font-medium leading-none">42</div> */}
          </div>
          <div className="relative space-y-8">
            <div>
              <h3 className="text-[20px] leading-6 md:text-[32px] font-medium mb-2 md:leading-[38px] ">
                Get in Touch
              </h3>
              <p className="mt-2 text-white text-xs md:text-lg">
                It&apos;s only when you communicate that you learn to come
                together.
              </p>
            </div>
            <div className="space-y-4 md:space-y-8">
              <div className="flex items-center gap-3 ">
                <Phone className="h-4 w-4 md:h-6 md:w-6" />
                <span className="text-sm md:text-[20px] mt-1 font-medium">
                  ************
                </span>
              </div>
              <div className="flex items-center gap-3 ">
                <Mail className="h-4 w-4 md:h-6 md:w-6" />
                <span className="text-sm md:text-[20px] mt-1 font-medium">
                  <EMAIL>
                </span>
              </div>
              <div className="flex items-start gap-3 ">
                <MapPin className="h-4 w-4 md:h-6 md:w-6" />
                <div className="text-sm md:text-[20px] leading-7 font-medium">
                  <p>Forty Two Estates</p>
                  <p>No.775, 42 High Street</p>
                  <p>100 ft road, HAL 2nd Stage,</p>
                  <p>Indiranagar, Bangalore</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
