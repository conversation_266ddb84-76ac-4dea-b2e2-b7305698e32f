import { Button } from "@/components/ui/button";
import React from "react";

function scrollToSection(sectionId: string) {
  // event.preventDefault(); // Prevent default anchor link behavior
  const section = document.getElementById(sectionId);

  if (section) {
    window.scrollTo({
      top: section.offsetTop, // Scroll to section's top position
      behavior: "smooth", // Smooth scrolling effect
    });
  }
}

function SectionEnquire({
  setSubject,
}: {
  setSubject: (subject: string) => void;
}) {
  const handleOnClick = (subject: string) => {
    console.log(subject);
    setSubject(subject);
    scrollToSection("contact-form-42");
  };
  return (
    <div className="container mx-auto px-4">
      <div className="flex flex-col md:flex-row gap-10 items-center">
        <div className="flex-1 space-y-4 text-center md:text-left">
          <h2 className="text-[20px] md:text-[32px] font-medium">Referrals</h2>
          <p className="font-normal text-sm md:text-lg text-[#484848] md:leading-8">
            Refer somebody you know for purchase our properties or for joint
            ventures and get yourself rewarded
          </p>
          <Button onClick={() => handleOnClick("referrals")}>
            Contact now
          </Button>
        </div>
        <div className="w-[148px] h-[1px] md:w-[1px] md:h-[148px]  bg-primary"></div>
        <div className="flex-1 space-y-4 text-center md:text-left">
          <h2 className="text-[20px] md:text-[32px] font-medium">
            Vendor Registration
          </h2>
          <p className="font-normal text-sm  md:text-lg text-[#484848] md:leading-8">
            Contact us for being a part of 42 Estates certified vendor list, and
            let us grow together.
          </p>
          <Button onClick={() => handleOnClick("vendor-registration")}>
            Contact now
          </Button>
        </div>
        <div className="w-[148px] h-[1px] md:w-[1px] md:h-[148px]  bg-primary"></div>
        <div className="flex-1 space-y-4 text-center md:text-left">
          <h2 className="text-[20px] md:text-[32px]   font-medium">
            General Enquiries
          </h2>
          <p className="font-normal text-sm  md:text-lg text-[#484848] md:leading-8">
            It&apos;s only when you communicate, that we know you have some
            queries to be answered.
          </p>
          <Button onClick={() => handleOnClick("general-enquiries")}>
            Contact now
          </Button>
        </div>
      </div>
    </div>
  );
}

export default SectionEnquire;
