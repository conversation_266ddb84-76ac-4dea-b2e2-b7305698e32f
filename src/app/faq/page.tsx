import { getAllBlogs, getFaqs } from "@/data/loaders";
import React, { Suspense } from "react";

import { Banner } from "@/components/banner";
import ExpandableEnquiryForm from "@/components/expandable-form";
import MobileFloatingBar from "@/components/mobile-floating-cta";
import FAQSection from "@/components/faq-section";

export const revalidate = 60;

const Content = () => {
  return <h2 className="text-5xl font-medium mb-4">Faq&apos;s</h2>;
};

async function CareersPage() {
  const faqs = await getFaqs();

  return (
    <div className="w-full">
      <Banner img="/blog.webp" content={Content} />

      <div className="h-24 w-full"></div>
      <div className="container mx-auto px-4">
        <FAQSection faqs={faqs} />
      </div>
      <div className="h-24 w-full"></div>
      <Suspense>
        <ExpandableEnquiryForm />
        <MobileFloatingBar />
      </Suspense>
    </div>
  );
}

export default CareersPage;
