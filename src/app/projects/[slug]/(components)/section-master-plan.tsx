import { MasterPlan } from "@/components/MasterPlan";
import React from "react";

export default function SectionMasterPlan({ projectAttributes }) {
  return (
    <div>
      <h2 className="text-[28px] text-center leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 lg:mb-6">
        Master Plan
      </h2>

      <MasterPlan
        image={
          process.env.NEXT_PUBLIC_STRAPI_URL +
          projectAttributes?.MasterPlan?.data?.attributes?.url
        }
      />
    </div>
  );
}
