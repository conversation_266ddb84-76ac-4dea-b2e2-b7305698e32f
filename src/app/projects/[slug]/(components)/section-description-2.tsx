import { Section } from "@/components/section";

import parse from "html-react-parser";

export default function SectionDescription2({ projectAttributes }) {
  return (
    <div className="w-full bg-secondary">
      <div className={"mx-auto container px-4 py-8 md:py-16"}>
        <Section
          title={projectAttributes?.propery_title}
          description={
            projectAttributes?.project_content2 &&
            (parse(projectAttributes?.project_content2) as string)
          }
          // features={projectAttributes.features_list}
          mobileImagePosition="after"
          imagePosition="left"
          image={
            projectAttributes?.DescriptionImage2?.data?.attributes.url
              ? process.env.NEXT_PUBLIC_STRAPI_URL +
                projectAttributes.DescriptionImage2.data.attributes.url
              : process.env.NEXT_PUBLIC_STRAPI_URL +
                projectAttributes?.Project_image?.data?.attributes.url
          }
        />
      </div>
    </div>
  );
}
