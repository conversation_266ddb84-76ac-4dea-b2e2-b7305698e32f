import { ProjectSlider } from "@/app/(components)/(projects)/project-slider";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import React from "react";

export default function SectionOtherProjects({ allProjectsExpectCurrent }) {
  return (
    <div className="w-full ">
      <div className="container mx-auto px-4">
        <h2 className="text-[28px] text-center leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-4 lg:mb-6">
          Other Projects
        </h2>
      </div>
      <div className="md:h-4 w-full"></div>
      <ProjectSlider projects={allProjectsExpectCurrent} />
      <div className="container mx-auto px-4 md:hidden text-center">
        <Link href="/projects">
          <Button>View More</Button>
        </Link>
      </div>
    </div>
  );
}
