import { Section } from "@/components/section";

import { Badge } from "@/components/ui/badge";
import parse from "html-react-parser";

export default function SectionDescription1({ projectAttributes }) {
  return (
    <div className="container mx-auto px-4">
      <Section
        title={projectAttributes?.subtitle}
        description={
          projectAttributes?.project_content1 ||
          projectAttributes?.details_content
            ? (parse(
                (projectAttributes?.project_content1 || "") +
                  (projectAttributes?.project_content1 &&
                  projectAttributes?.details_content
                    ? "</br>"
                    : "") +
                  (projectAttributes?.details_content || "")
              ) as string)
            : ""
        }
        mobileImagePosition="after"
        imagePosition="right"
        image={
          projectAttributes?.Project_image?.data?.attributes.url
            ? process.env.NEXT_PUBLIC_STRAPI_URL +
              projectAttributes?.Project_image?.data?.attributes.url
            : null
        }
      />
      <div className="h-10 w-full"></div>
      <div className="flex flex-wrap justify-center gap-4 md:px-4 py-2">
        {projectAttributes.features_list.map((feature, i) => (
          <Badge
            key={i}
            variant="secondary"
            className="w-full text-left md:w-auto inline-flex px-4 py-2 md:text-[16px] md:leading-[22.72px] font-normal md:text-center bg-badge-gradient rounded-full whitespace-normal"
          >
            {feature.Content}
          </Badge>
        ))}
      </div>
    </div>
  );
}
