// Types
interface Project {
  id: number;
  attributes: {
    category: { data: { attributes: { name: string } } };
    location: string;
    Slug: string;
    title: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    superarea: string;
    sftarea: string;
    carpetarea: string;
    bmrda: string;
    Rera: string;
    address: string;
    phone: string;
    offer_title: string;
    offer_content: string;
    details_content: string;
    approved: null | string;
    propery_title: string;
    villaroom: null | string;
    luxurious_title: string;
    project_content1: string;
    project_content2: string;
    project_title: string;
    subtitle: string;
    bhk: null | string;
    Sell_Do_Form: string;
    Location_Map: string;
    blogs: {
      data: any[];
    };
    faqs: {
      data: {
        id: string;
        attributes: {
          Questions: string;
          Answers: string;
        };
      }[];
    };
    MasterPlan: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
    image: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
    status: {
      data: {
        attributes: {
          name: string;
        };
      };
    };
    ProjectUpdates: {
      data: [
        {
          attributes: {
            url: string;
          };
        }
      ];
    };
    Project_image: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
    Type: string;
    price: {
      price: string;
    };
    features_list: Array<{
      id: string;
      Content: string;
    }>;
  };
}

interface ProjectResponse {
  data: Project[];
}
