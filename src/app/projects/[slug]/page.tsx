import { getProject, getProjects } from "@/data/loaders";

import LocationMap from "@/components/location-map";

import { Banner } from "@/components/banner";
import { MapPinned } from "lucide-react";
import SectionDescription1 from "./(components)/section-description-1";
import SectionDescription2 from "./(components)/section-description-2";
import SectionMasterPlan from "./(components)/section-master-plan";
import SectionFloorPlan from "./(components)/section-floor-plan";
import SectionProjectUpdates from "./(components)/section-project-update";
import SectionOtherProjects from "./(components)/section-other-projects";
import SectionBlog from "@/app/(components)/section-blog";
import SectionFaqs from "./(components)/section-faqs";
import Image from "next/image";
import ExpandableEnquiryForm from "@/components/expandable-form";
import MobileFloatingBar from "@/components/mobile-floating-cta";
import seoGenerator from "@/components/seo";

import NotFound from "@/components/NotFound";
import { Suspense } from "react";

export const revalidate = 3600;
export const dynamicParams = true;

export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}) {
  try {
    const { slug } = await Promise.resolve(params);

    console.log("Slug", slug);

    if (!slug) {
      console.log("Slug parameter is required");
      return {}; // Return empty metadata if no slug
    }

    const projects = (await getProject(slug)) as ProjectResponse;

    if (!projects?.data?.[0]) {
      console.log(`Project not found for slug: ${slug}`);
      return {}; // Return empty metadata if no project found
    }

    const project = projects.data[0];
    const projectAttributes = project.attributes;
    console.log("project Atri", projectAttributes);
    const seoData = {
      metaTitle: projectAttributes?.Seo?.metaTitle || projectAttributes.title,
      MetaDescription:
        projectAttributes?.Seo?.MetaDescription ||
        projectAttributes.project_content1,
      Keywords:
        projectAttributes?.Seo?.Keywords ||
        projectAttributes.features_list.toString(),
      ShareImage:
        projectAttributes?.Seo?.ShareImage || projectAttributes.Project_image,
    };

    return seoGenerator(seoData);
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {}; // Return empty metadata on error
  }
}

// Static params generation
export async function generateStaticParams() {
  try {
    const projects = (await getProjects()) as ProjectResponse;

    if (!projects?.data || !Array.isArray(projects.data)) {
      console.error("No projects data available for static generation");
      return [];
    }

    const params = projects.data
      .filter((item: Project) => {
        if (!item?.attributes?.Slug) {
          console.warn("Project found with missing slug:", item);
          return false;
        }
        return true;
      })
      .map((item: Project) => ({
        slug: String(item.attributes.Slug),
      }));

    console.log(`Generating static params for ${params.length} projects`);
    return params;
  } catch (error) {
    console.error("Error generating static params:", error);
    return [];
  }
}

const Content = ({
  title,
  projectlogo,
  address,
  approvals,
  price,
  status,
  specifications,
  category,
}) => {
  return (
    <div>
      {projectlogo?.data?.attributes?.url ? (
        <Image
          src={`${process.env.NEXT_PUBLIC_STRAPI_URL}${projectlogo.data.attributes.url}`}
          alt={title || "Project Image"}
          width={300} // Default width for larger screens
          height={0} // Auto height based on aspect ratio
          className="w-[120px] sm:w-[150px] md:w-[200px] lg:w-[250px] h-auto object-cover"
        />
      ) : (
        <h1 className="text-[28px] leading-[36px] md:text-[40px] md:leading-[48px] lg:text-[52px] lg:leading-[62px] font-bold text-left">
          {title}
        </h1>
      )}
      <div className="h-4"></div>

      {!!address && (
        <div className="flex gap-1">
          <MapPinned size={24} className="w-12" />
          <p className="font-normal text-xs leading-[14.4px] md:text-[16px] md:leading-[23px]">
            {address}
          </p>
        </div>
      )}
      {category.data.attributes.slug !== "hospitality" && (
        <>
          <div className="h-4"></div>
          <div className="w-full h-[1px] bg-white"></div>
          <div className="h-4"></div>

          <div>
            <h4 className="text-2xl font-medium">{status}</h4>
          </div>

          <div className="h-4"></div>
          <div>
            {approvals?.map((item, i) => (
              <p
                key={i}
                className="font-normal text-xs leading-[14.4px] md:text-[16px] md:leading-[23px]"
              >
                {item}
              </p>
            ))}
          </div>
          <div className="h-8"></div>
          <div>
            <h4 className="text-lg leading-[26px] md:text-3xl md:leading-[48.3px] font-bold">
              <span>{`${price}`}</span>
              {specifications && (
                <>
                  <span>, </span>
                  <span>{`${specifications}`}</span>
                </>
              )}
            </h4>
          </div>
        </>
      )}
    </div>
  );
};
// Page component
export default async function Page({ params }: { params: { slug: string } }) {
  try {
    const { slug } = await Promise.resolve(params);

    if (!slug) {
      throw new Error("Slug parameter is required");
    }

    const projects = (await getProject(slug)) as ProjectResponse;

    if (!projects?.data?.[0]) {
      throw new Error(`Project not found for slug: ${slug}`);
    }

    const project = projects.data[0];

    const projectAttributes = project.attributes;
    const projectUpdates = project.attributes?.ProjectUpdates?.data?.map(
      (item) => {
        return process.env.NEXT_PUBLIC_STRAPI_URL + item.attributes.url;
      }
    );

    const allProjects = (await getProjects()) as ProjectResponse;

    const allProjectsExpectCurrent = {
      data: allProjects.data.filter((item) => item.attributes.Slug !== slug),
    };

    return (
      <main className="w-full ">
        <Banner
          content={() => (
            <Content
              title={projectAttributes?.title}
              address={projectAttributes?.address}
              approvals={[projectAttributes?.bmrda, projectAttributes?.Rera]}
              price={projectAttributes?.price?.price}
              status={projectAttributes?.status?.data?.attributes?.name}
              specifications={projectAttributes.bhk}
              category={projectAttributes.category}
              projectlogo={projectAttributes.projectlogo}
            />
          )}
          img={
            process.env.NEXT_PUBLIC_STRAPI_URL +
            projectAttributes?.image?.data?.attributes.url
          }
        />

        <div className="h-12 md:h-24 w-full"></div>
        <SectionDescription1 projectAttributes={projectAttributes} />

        {!!projectAttributes?.project_content2 && (
          <>
            <div className="h-12 md:h-24 w-full"></div>
            <SectionDescription2 projectAttributes={projectAttributes} />
          </>
        )}

        {!!projectAttributes?.MasterPlan?.data?.attributes?.url && (
          <>
            <div className="h-12 md:h-24 w-full"></div>
            <SectionMasterPlan projectAttributes={projectAttributes} />
          </>
        )}

        {projectAttributes?.Type.length > 0 && (
          <>
            <div className="h-12 md:h-24 w-full"></div>
            <SectionFloorPlan projectAttributes={projectAttributes} />
          </>
        )}

        {projectUpdates && projectUpdates.length > 6 && (
          <>
            <div className="h-12 md:h-24 w-full"></div>
            <SectionProjectUpdates projectUpdates={projectUpdates} />
          </>
        )}

        {allProjectsExpectCurrent.data.length > 0 && (
          <>
            <div className="h-12 md:h-24 w-full"></div>
            <SectionOtherProjects
              allProjectsExpectCurrent={allProjectsExpectCurrent}
            />
          </>
        )}

        {project.attributes.blogs.data.length > 0 && (
          <>
            <div className="h-16 md:h-24 w-full"></div>
            <SectionBlog data={project.attributes.blogs.data} />
          </>
        )}

        {project.attributes.faqs.data.length > 0 && (
          <>
            <div className="h-12 md:h-24 w-full"></div>
            <SectionFaqs faqs={project.attributes.faqs} />
          </>
        )}

        {project.attributes.Location_Map && (
          <>
            <div className="h-12 md:h-24 w-full"></div>
            <div className="container mx-auto px-4">
              <LocationMap
                src={project.attributes.Location_Map}
                title="Location"
              />
            </div>
          </>
        )}
        <div className="h-12 md:h-24 w-full"></div>
        <Suspense>
          <ExpandableEnquiryForm
            projectId={project.attributes.Sell_Do_Form}
            // srd={srd as string}
            otherInfo={project.attributes.title}
          />
          <MobileFloatingBar
            projectId={project.attributes.Sell_Do_Form}
            // srd={srd as string}
            otherInfo={project.attributes.title}
          />
        </Suspense>
      </main>
    );
  } catch (error) {
    console.error("Error rendering project page:", error);
    return <NotFound />;
  }
}
