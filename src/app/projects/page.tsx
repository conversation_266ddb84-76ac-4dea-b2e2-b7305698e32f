import { Banner } from "@/components/banner";
import { ProjectGrid } from "@/components/project-grid";

import { getProjects } from "@/data/loaders";

export const revalidate = 60;

const Content = () => {
  return (
    <div>
      <h1 className="text-[42px] leading-[50.4px] font-medium md:text-[52px] md:leading-[62.4px] md:font-bold">
        Your Address Is Not Just A Pin Code, <br />
        It Is A Lifestyle.
      </h1>
    </div>
  );
};

export default async function ProjectsPage() {
  const projectData = await getProjects();
  // console.log(projectData);
  return (
    <div>
      <Banner size="medium" content={Content} />

      <div
        className="bg-secondary relative z-5"
        style={{ marginTop: "-150px" }}
      >
        <div className="h-12 md:h-24 w-full"> </div>
        <div className="container mx-auto px-4" style={{ marginTop: "150px" }}>
          <h2 className="text-[28px] leading-[33.6px] md:text-[42px] md:leading-[50.4px] font-medium mb-3 md:mb-6">
            <span className="text-black">Our </span>
            <span className="text-primary">Projects</span>
          </h2>

          <p className="max-w-4xl text-xs leading-[22px] md:text-lg font-normal md:leading-8">
            To epitomize the real estate sector, we the 42 Estates build the
            best homes the market has ever witnessed using our limitless
            imagination, resources and brilliance. We work relentlessly to
            pioneer new trends and give shapes to possibilities that no other
            firm in our business has ever thought of.
          </p>
        </div>
        <div className="h-4 md:h-8 w-full"> </div>
        <ProjectGrid projects={projectData} />
        <div className="h-40 w-full"> </div>
      </div>
    </div>
  );
}
