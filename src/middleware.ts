import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();
  const srd = url.searchParams.get("srd");

  // If we're navigating to a page and we have the srd parameter in the current URL
  if (srd) {
    // Store srd in a cookie for persistence
    const response = NextResponse.next();
    response.cookies.set("srd", srd, {
      path: "/",
      maxAge: 60 * 60 * 24 * 30, // 30 days
      sameSite: "lax",
    });
    return response;
  }

  // If we don't have srd in the URL but we have it in cookies, add it to the URL
  const srdFromCookie = request.cookies.get("srd")?.value;
  if (srdFromCookie && !srd && !url.pathname.includes("/_next")) {
    url.searchParams.set("srd", srdFromCookie);
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
