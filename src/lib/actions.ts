"use server";

export async function submitContactForm(formData: FormData) {
  // Simulate a delay to show loading state
  await new Promise((resolve) => setTimeout(resolve, 1000));

  const name = formData.get("name");
  const phone = formData.get("phone");
  const email = formData.get("email");
  const subject = formData.get("subject");
  const message = formData.get("message");

  // Add your form submission logic here
  // For example, sending an email or saving to a database

  return { success: true };
}
