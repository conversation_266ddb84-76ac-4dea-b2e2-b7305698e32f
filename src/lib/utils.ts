import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getStrapiURL() {
  return process.env.STRAPI_URL ?? "http://localhost:1337";
}

export const countryCodes = [
  { code: "91", country: "India", flag: "🇮🇳" },
  { code: "1", country: "USA", flag: "🇺🇸" },
  { code: "44", country: "UK", flag: "🇬🇧" },
  { code: "86", country: "China", flag: "🇨🇳" },
  { code: "81", country: "Japan", flag: "🇯🇵" },
  { code: "49", country: "Germany", flag: "🇩🇪" },
  { code: "33", country: "France", flag: "🇫🇷" },
  { code: "7", country: "Russia", flag: "🇷🇺" },
  { code: "39", country: "Italy", flag: "🇮🇹" },
  { code: "34", country: "Spain", flag: "🇪🇸" },
  { code: "55", country: "Brazil", flag: "🇧🇷" },
  { code: "61", country: "Australia", flag: "🇦🇺" },
  { code: "82", country: "South Korea", flag: "🇰🇷" },
  { code: "52", country: "Mexico", flag: "🇲🇽" },
  { code: "31", country: "Netherlands", flag: "🇳🇱" },
  { code: "46", country: "Sweden", flag: "🇸🇪" },
  { code: "41", country: "Switzerland", flag: "🇨🇭" },
  { code: "20", country: "Egypt", flag: "🇪🇬" },
  { code: "90", country: "Turkey", flag: "🇹🇷" },
  { code: "27", country: "South Africa", flag: "🇿🇦" },
  { code: "65", country: "Singapore", flag: "🇸🇬" },
  { code: "966", country: "Saudi Arabia", flag: "🇸🇦" },
  { code: "971", country: "United Arab Emirates", flag: "🇦🇪" },
  { code: "92", country: "Pakistan", flag: "🇵🇰" },
  { code: "880", country: "Bangladesh", flag: "🇧🇩" },
  { code: "62", country: "Indonesia", flag: "🇮🇩" },
];
